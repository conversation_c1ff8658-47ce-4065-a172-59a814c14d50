/**
 * Student Communication APIs Test Script (Node.js)
 * This script tests all existing student communication functionality
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:5001';
const AUTH_TOKEN = 'Bearer test-token'; // Replace with actual JWT token

// Helper function to make HTTP requests
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(BASE_URL + path);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Authorization': AUTH_TOKEN,
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test functions
async function testStudentAnnouncements() {
  console.log('📢 Testing Student Announcements API');
  console.log('-----------------------------------');
  
  try {
    // Test 1: Get student announcements
    console.log('1. Testing GET /api/announcements/student');
    const result1 = await makeRequest('GET', '/api/announcements/student');
    console.log(`   Status: ${result1.status}`);
    console.log(`   Response: ${JSON.stringify(result1.data, null, 2).substring(0, 200)}...`);
    
    // Test 2: Get student announcements with course filter
    console.log('\n2. Testing GET /api/announcements/student with course filter');
    const result2 = await makeRequest('GET', '/api/announcements/student?courseId=test-course-id');
    console.log(`   Status: ${result2.status}`);
    console.log(`   Response: ${JSON.stringify(result2.data, null, 2).substring(0, 200)}...`);
    
  } catch (error) {
    console.error('Error testing announcements:', error.message);
  }
}

async function testStudentMessages() {
  console.log('\n💬 Testing Student Messages APIs');
  console.log('-------------------------------');
  
  try {
    // Test 3: Get inbox messages
    console.log('3. Testing GET /api/messages/inbox');
    const result3 = await makeRequest('GET', '/api/messages/inbox');
    console.log(`   Status: ${result3.status}`);
    console.log(`   Response: ${JSON.stringify(result3.data, null, 2).substring(0, 200)}...`);
    
    // Test 4: Get inbox with filters
    console.log('\n4. Testing GET /api/messages/inbox with filters');
    const result4 = await makeRequest('GET', '/api/messages/inbox?status=unread&page=1&limit=10');
    console.log(`   Status: ${result4.status}`);
    console.log(`   Response: ${JSON.stringify(result4.data, null, 2).substring(0, 200)}...`);
    
    // Test 5: Get sent messages
    console.log('\n5. Testing GET /api/messages/sent');
    const result5 = await makeRequest('GET', '/api/messages/sent');
    console.log(`   Status: ${result5.status}`);
    console.log(`   Response: ${JSON.stringify(result5.data, null, 2).substring(0, 200)}...`);
    
    // Test 6: Create message
    console.log('\n6. Testing POST /api/messages (Create Message)');
    const messageData = {
      recipientId: 'test-recipient-id',
      subject: 'Test Message',
      content: 'This is a test message from student',
      messageType: 'personal',
      priority: 'normal'
    };
    const result6 = await makeRequest('POST', '/api/messages', messageData);
    console.log(`   Status: ${result6.status}`);
    console.log(`   Response: ${JSON.stringify(result6.data, null, 2).substring(0, 200)}...`);
    
    // Test 7: Reply to message
    console.log('\n7. Testing POST /api/messages/:id/reply (Reply to Message)');
    const replyData = {
      subject: 'Re: Test Message',
      content: 'This is a reply to the test message'
    };
    const result7 = await makeRequest('POST', '/api/messages/test-message-id/reply', replyData);
    console.log(`   Status: ${result7.status}`);
    console.log(`   Response: ${JSON.stringify(result7.data, null, 2).substring(0, 200)}...`);
    
    // Test 8: Get conversation thread
    console.log('\n8. Testing GET /api/messages/thread/:threadId');
    const result8 = await makeRequest('GET', '/api/messages/thread/test-thread-id');
    console.log(`   Status: ${result8.status}`);
    console.log(`   Response: ${JSON.stringify(result8.data, null, 2).substring(0, 200)}...`);
    
    // Test 9: Get unread count
    console.log('\n9. Testing GET /api/messages/unread-count');
    const result9 = await makeRequest('GET', '/api/messages/unread-count');
    console.log(`   Status: ${result9.status}`);
    console.log(`   Response: ${JSON.stringify(result9.data, null, 2).substring(0, 200)}...`);
    
  } catch (error) {
    console.error('Error testing messages:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🧪 Testing Student Communication APIs');
  console.log('=====================================\n');
  
  await testStudentAnnouncements();
  await testStudentMessages();
  
  console.log('\n✅ API Testing Complete!');
  console.log('========================');
  console.log('\n📊 Expected Results:');
  console.log('- All APIs should return HTTP 200 or 401 (if authentication fails)');
  console.log('- Response should be valid JSON');
  console.log('- Error responses should have proper error structure');
  console.log('\n🔧 To run this test:');
  console.log('1. Make sure server is running: npm run dev');
  console.log('2. Replace "test-token" with actual JWT token');
  console.log('3. Run: node test-student-communication-apis.js');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testStudentAnnouncements, testStudentMessages, runTests };
