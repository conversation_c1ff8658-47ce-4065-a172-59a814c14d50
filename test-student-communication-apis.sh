#!/bin/bash

# Student Communication APIs Test Script
# This script tests all existing student communication functionality

echo "🧪 Testing Student Communication APIs"
echo "======================================"

BASE_URL="http://localhost:5001"
AUTH_TOKEN="Bearer test-token"  # Replace with actual JWT token

echo ""
echo "📢 Testing Student Announcements API"
echo "-----------------------------------"

# Test 1: Get student announcements
echo "1. Testing GET /api/announcements/student"
curl -s -X GET "$BASE_URL/api/announcements/student" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "2. Testing GET /api/announcements/student with course filter"
curl -s -X GET "$BASE_URL/api/announcements/student?courseId=test-course-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "💬 Testing Student Messages APIs"
echo "-------------------------------"

# Test 3: Get inbox messages
echo "3. Testing GET /api/messages/inbox"
curl -s -X GET "$BASE_URL/api/messages/inbox" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "4. Testing GET /api/messages/inbox with filters"
curl -s -X GET "$BASE_URL/api/messages/inbox?status=unread&page=1&limit=10" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "5. Testing GET /api/messages/sent"
curl -s -X GET "$BASE_URL/api/messages/sent" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "6. Testing POST /api/messages (Create Message)"
curl -s -X POST "$BASE_URL/api/messages" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "recipientId": "test-recipient-id",
    "subject": "Test Message",
    "content": "This is a test message from student",
    "messageType": "personal",
    "priority": "normal"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "7. Testing POST /api/messages/:id/reply (Reply to Message)"
curl -s -X POST "$BASE_URL/api/messages/test-message-id/reply" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Re: Test Message",
    "content": "This is a reply to the test message"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "8. Testing GET /api/messages/thread/:threadId"
curl -s -X GET "$BASE_URL/api/messages/thread/test-thread-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "9. Testing GET /api/messages/unread-count"
curl -s -X GET "$BASE_URL/api/messages/unread-count" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -20

echo ""
echo "✅ API Testing Complete!"
echo "========================"
echo ""
echo "📊 Expected Results:"
echo "- All APIs should return HTTP 200 or 401 (if authentication fails)"
echo "- Response should be valid JSON"
echo "- Error responses should have proper error structure"
echo ""
echo "🔧 To run this test:"
echo "1. Make sure server is running: npm run dev"
echo "2. Replace 'test-token' with actual JWT token"
echo "3. Run: chmod +x test-student-communication-apis.sh"
echo "4. Run: ./test-student-communication-apis.sh"
