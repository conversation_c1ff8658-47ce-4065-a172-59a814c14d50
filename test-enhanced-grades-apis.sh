#!/bin/bash

# Enhanced Student Grades APIs Test Script
# This script tests the enhanced student grades functionality

echo "🧪 Testing Enhanced Student Grades APIs"
echo "======================================="

BASE_URL="http://localhost:5001"
AUTH_TOKEN="Bearer test-token"  # Replace with actual JWT token

echo ""
echo "📊 Testing Enhanced Grades API"
echo "-----------------------------"

# Test 1: Enhanced grades with project and checkpoint data
echo "1. Testing GET /api/grades (Enhanced)"
curl -s -X GET "$BASE_URL/api/grades?enhanced=true" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "2. Testing GET /api/grades with course filter"
curl -s -X GET "$BASE_URL/api/grades?enhanced=true&courseId=test-course-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "3. Testing GET /api/grades with pagination"
curl -s -X GET "$BASE_URL/api/grades?enhanced=true&page=1&limit=5" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "📈 Testing Student Grade Summary"
echo "-------------------------------"

# Test 4: Student grade summary
echo "4. Testing GET /api/grades/student/summary"
curl -s -X GET "$BASE_URL/api/grades/student/summary" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "5. Testing GET /api/grades/student/summary with course filter"
curl -s -X GET "$BASE_URL/api/grades/student/summary?courseId=test-course-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "🎯 Testing Project Grade Details"
echo "-------------------------------"

# Test 6: Project grade details
echo "6. Testing GET /api/grades/project/:projectId"
curl -s -X GET "$BASE_URL/api/grades/project/test-project-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "7. Testing GET /api/grades/project/:projectId (Invalid ID)"
curl -s -X GET "$BASE_URL/api/grades/project/invalid-project-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -10

echo ""
echo "📋 Testing Checkpoint Grades"
echo "---------------------------"

# Test 8: Checkpoint grades
echo "8. Testing GET /api/grades/checkpoints/:projectId"
curl -s -X GET "$BASE_URL/api/grades/checkpoints/test-project-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -30

echo ""
echo "9. Testing GET /api/grades/checkpoints/:projectId (Invalid ID)"
curl -s -X GET "$BASE_URL/api/grades/checkpoints/invalid-project-id" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -10

echo ""
echo "🔒 Testing Authentication & Authorization"
echo "----------------------------------------"

# Test 10: Without authentication
echo "10. Testing GET /api/grades without authentication"
curl -s -X GET "$BASE_URL/api/grades" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -10

echo ""
echo "11. Testing GET /api/grades with invalid token"
curl -s -X GET "$BASE_URL/api/grades" \
  -H "Authorization: Bearer invalid-token" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  | head -10

echo ""
echo "⚡ Testing Performance"
echo "--------------------"

# Test 12: Performance test with multiple requests
echo "12. Testing performance with multiple concurrent requests"
for i in {1..5}; do
  curl -s -X GET "$BASE_URL/api/grades?enhanced=true" \
    -H "Authorization: $AUTH_TOKEN" \
    -H "Content-Type: application/json" \
    -w "Request $i - HTTP Status: %{http_code} - Time: %{time_total}s\n" &
done
wait

echo ""
echo "✅ Enhanced Grades API Testing Complete!"
echo "======================================="
echo ""
echo "📊 Expected Results:"
echo "- Enhanced grades should include project progress and checkpoint data"
echo "- Student summary should show overall grade statistics"
echo "- Project details should show comprehensive project information"
echo "- Checkpoint grades should show individual checkpoint progress"
echo "- Authentication should be required for all endpoints"
echo "- Invalid IDs should return appropriate error responses"
echo ""
echo "🔧 To run this test:"
echo "1. Make sure server is running: npm run dev"
echo "2. Replace 'test-token' with actual JWT token"
echo "3. Run: chmod +x test-enhanced-grades-apis.sh"
echo "4. Run: ./test-enhanced-grades-apis.sh"
echo ""
echo "📈 Enhanced Features Tested:"
echo "✅ Project-wise grading data"
echo "✅ Checkpoint progress and grades"
echo "✅ Grade analytics and comparisons"
echo "✅ Student grade summary"
echo "✅ Project-specific grade details"
echo "✅ Checkpoint-specific grades"
echo "✅ Authentication and authorization"
echo "✅ Error handling and validation"
echo "✅ Performance and scalability"
