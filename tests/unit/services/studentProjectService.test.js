import { jest } from '@jest/globals';
import studentProjectService from '../../../src/services/studentProjectService.js';
import { StudentProjectProgress, Checkpoint, CheckpointProgress, Project, Course, User } from '../../../src/models/associations.js';

// Mock the models
jest.mock('../../../src/models/associations.js', () => ({
  StudentProjectProgress: {
    findOne: jest.fn(),
    create: jest.fn(),
    findAll: jest.fn()
  },
  Checkpoint: {
    findAll: jest.fn()
  },
  CheckpointProgress: {},
  Project: {
    findByPk: jest.fn()
  },
  Course: {},
  User: {}
}));

// Mock logger
jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}));

describe('StudentProjectService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getStudentProgress', () => {
    it('should return student progress when found', async () => {
      const mockProgress = {
        id: 'progress-1',
        student_id: 'student-1',
        project_id: 'project-1',
        progress_percentage: 65.5,
        current_phase: 'Implementation',
        time_spent_hours: 13.2,
        last_activity: new Date('2024-01-15T10:30:00Z'),
        status: 'in_progress',
        enrollment_date: new Date('2024-01-01T00:00:00Z'),
        start_date: new Date('2024-01-02T09:00:00Z'),
        completion_date: null,
        grade: null,
        feedback: null
      };

      StudentProjectProgress.findOne.mockResolvedValue(mockProgress);

      const result = await studentProjectService.getStudentProgress('project-1', 'student-1');

      expect(StudentProjectProgress.findOne).toHaveBeenCalledWith({
        where: { 
          project_id: 'project-1', 
          student_id: 'student-1' 
        }
      });

      expect(result).toEqual({
        progressPercentage: 65.5,
        currentPhase: 'Implementation',
        timeSpentHours: 13.2,
        lastActivity: new Date('2024-01-15T10:30:00Z'),
        status: 'in_progress',
        enrollmentDate: new Date('2024-01-01T00:00:00Z'),
        startDate: new Date('2024-01-02T09:00:00Z'),
        completionDate: null,
        grade: null,
        feedback: null
      });
    });

    it('should return default progress when not found', async () => {
      StudentProjectProgress.findOne.mockResolvedValue(null);

      const result = await studentProjectService.getStudentProgress('project-1', 'student-1');

      expect(result).toEqual({
        progressPercentage: 0,
        currentPhase: null,
        timeSpentHours: 0,
        lastActivity: null,
        status: 'not_started',
        enrollmentDate: null,
        startDate: null,
        completionDate: null,
        grade: null,
        feedback: null
      });
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Database error');
      StudentProjectProgress.findOne.mockRejectedValue(error);

      await expect(studentProjectService.getStudentProgress('project-1', 'student-1'))
        .rejects.toThrow('Database error');
    });
  });

  describe('getProjectCheckpoints', () => {
    it('should return checkpoints with student progress', async () => {
      const mockCheckpoints = [
        {
          id: 'checkpoint-1',
          title: 'Project Setup',
          description: 'Set up the project environment',
          checkpoint_number: 1,
          due_date: new Date('2024-01-10T23:59:59Z'),
          weight_percentage: 20.0,
          is_required: true,
          studentProgress: [{
            status: 'completed',
            submitted_at: new Date('2024-01-08T14:30:00Z'),
            grade: 95.0,
            feedback: 'Excellent work!'
          }]
        },
        {
          id: 'checkpoint-2',
          title: 'Implementation',
          description: 'Implement core functionality',
          checkpoint_number: 2,
          due_date: new Date('2024-01-20T23:59:59Z'),
          weight_percentage: 30.0,
          is_required: true,
          studentProgress: [{
            status: 'in_progress',
            submitted_at: null,
            grade: null,
            feedback: null
          }]
        }
      ];

      Checkpoint.findAll.mockResolvedValue(mockCheckpoints);

      const result = await studentProjectService.getProjectCheckpoints('project-1', 'student-1');

      expect(Checkpoint.findAll).toHaveBeenCalledWith({
        where: { 
          project_id: 'project-1', 
          status: 'published' 
        },
        order: [['checkpoint_number', 'ASC']],
        include: [
          {
            model: CheckpointProgress,
            as: 'studentProgress',
            where: { student_id: 'student-1' },
            required: false
          }
        ]
      });

      expect(result).toEqual({
        total: 2,
        completed: 1,
        inProgress: 1,
        notStarted: 0,
        overdue: 0,
        details: [
          {
            id: 'checkpoint-1',
            title: 'Project Setup',
            description: 'Set up the project environment',
            checkpointNumber: 1,
            dueDate: new Date('2024-01-10T23:59:59Z'),
            weightPercentage: 20.0,
            isRequired: true,
            status: 'completed',
            submittedAt: new Date('2024-01-08T14:30:00Z'),
            grade: 95.0,
            feedback: 'Excellent work!'
          },
          {
            id: 'checkpoint-2',
            title: 'Implementation',
            description: 'Implement core functionality',
            checkpointNumber: 2,
            dueDate: new Date('2024-01-20T23:59:59Z'),
            weightPercentage: 30.0,
            isRequired: true,
            status: 'in_progress',
            submittedAt: null,
            grade: null,
            feedback: null
          }
        ]
      });
    });

    it('should handle overdue checkpoints', async () => {
      const pastDate = new Date('2024-01-01T23:59:59Z');
      const mockCheckpoints = [
        {
          id: 'checkpoint-1',
          title: 'Overdue Checkpoint',
          checkpoint_number: 1,
          due_date: pastDate,
          weight_percentage: 20.0,
          is_required: true,
          studentProgress: [{
            status: 'not_started',
            submitted_at: null,
            grade: null,
            feedback: null
          }]
        }
      ];

      Checkpoint.findAll.mockResolvedValue(mockCheckpoints);

      const result = await studentProjectService.getProjectCheckpoints('project-1', 'student-1');

      expect(result.overdue).toBe(1);
    });
  });

  describe('calculateProjectTimeline', () => {
    it('should calculate timeline correctly for active project', () => {
      const project = {
        start_date: new Date('2024-01-01T00:00:00Z'),
        end_date: new Date('2024-03-01T23:59:59Z'),
        due_date: new Date('2024-02-28T23:59:59Z'),
        created_at: new Date('2024-01-01T00:00:00Z')
      };

      const studentProgress = {
        progressPercentage: 65.5,
        status: 'in_progress'
      };

      // Mock current date
      const originalDate = Date;
      global.Date = jest.fn(() => new Date('2024-01-15T00:00:00Z'));
      global.Date.now = originalDate.now;

      const result = studentProjectService.calculateProjectTimeline(project, studentProgress);

      expect(result.duration).toBe(60); // 60 days
      expect(result.daysRemaining).toBe(44); // 44 days remaining
      expect(result.isOverdue).toBe(false);
      expect(result.progressStatus).toBe('on_track');

      // Restore original Date
      global.Date = originalDate;
    });

    it('should detect overdue projects', () => {
      const project = {
        start_date: new Date('2024-01-01T00:00:00Z'),
        end_date: new Date('2024-02-01T23:59:59Z'),
        due_date: new Date('2024-01-31T23:59:59Z'),
        created_at: new Date('2024-01-01T00:00:00Z')
      };

      const studentProgress = {
        progressPercentage: 30.0,
        status: 'in_progress'
      };

      // Mock current date to be after due date
      const originalDate = Date;
      global.Date = jest.fn(() => new Date('2024-02-05T00:00:00Z'));
      global.Date.now = originalDate.now;

      const result = studentProjectService.calculateProjectTimeline(project, studentProgress);

      expect(result.isOverdue).toBe(true);
      expect(result.progressStatus).toBe('overdue');

      // Restore original Date
      global.Date = originalDate;
    });

    it('should handle projects without end date', () => {
      const project = {
        start_date: new Date('2024-01-01T00:00:00Z'),
        end_date: null,
        due_date: new Date('2024-02-28T23:59:59Z'),
        created_at: new Date('2024-01-01T00:00:00Z')
      };

      const studentProgress = {
        progressPercentage: 50.0,
        status: 'in_progress'
      };

      const result = studentProjectService.calculateProjectTimeline(project, studentProgress);

      expect(result.duration).toBe(null);
      expect(result.daysRemaining).toBeGreaterThan(0);
    });
  });

  describe('getStudentProjectStats', () => {
    it('should return comprehensive student statistics', async () => {
      const mockProgressRecords = [
        {
          status: 'completed',
          progress_percentage: 100.0,
          time_spent_hours: 20.0,
          grade: 85.0,
          project: { id: 'project-1', title: 'Project 1' }
        },
        {
          status: 'in_progress',
          progress_percentage: 65.0,
          time_spent_hours: 15.0,
          grade: null,
          project: { id: 'project-2', title: 'Project 2' }
        },
        {
          status: 'not_started',
          progress_percentage: 0.0,
          time_spent_hours: 0.0,
          grade: null,
          project: { id: 'project-3', title: 'Project 3' }
        }
      ];

      StudentProjectProgress.findAll.mockResolvedValue(mockProgressRecords);

      const result = await studentProjectService.getStudentProjectStats('student-1');

      expect(result).toEqual({
        totalProjects: 3,
        notStarted: 1,
        inProgress: 1,
        completed: 1,
        overdue: 0,
        averageProgress: 55.0, // (100 + 65 + 0) / 3
        totalTimeSpent: 35.0, // 20 + 15 + 0
        averageGrade: 85.0 // Only completed project has grade
      });
    });

    it('should filter by course when provided', async () => {
      StudentProjectProgress.findAll.mockResolvedValue([]);

      await studentProjectService.getStudentProjectStats('student-1', 'course-1');

      expect(StudentProjectProgress.findAll).toHaveBeenCalledWith({
        where: { 
          student_id: 'student-1',
          course_id: 'course-1'
        },
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'due_date', 'status']
          }
        ]
      });
    });
  });

  describe('updateStudentProgress', () => {
    it('should update student progress successfully', async () => {
      const mockProgress = {
        id: 'progress-1',
        update: jest.fn().mockResolvedValue(true)
      };

      StudentProjectProgress.findOne.mockResolvedValue(mockProgress);

      const progressData = {
        progressPercentage: 75.0,
        currentPhase: 'Testing',
        timeSpentHours: 15.0,
        status: 'in_progress'
      };

      const result = await studentProjectService.updateStudentProgress(
        'project-1', 
        'student-1', 
        progressData
      );

      expect(StudentProjectProgress.findOne).toHaveBeenCalledWith({
        where: { 
          project_id: 'project-1', 
          student_id: 'student-1' 
        }
      });

      expect(mockProgress.update).toHaveBeenCalledWith({
        progress_percentage: 75.0,
        current_phase: 'Testing',
        time_spent_hours: 15.0,
        status: 'in_progress',
        last_activity: expect.any(Date)
      });

      expect(result).toBe(mockProgress);
    });

    it('should throw error when progress not found', async () => {
      StudentProjectProgress.findOne.mockResolvedValue(null);

      await expect(studentProjectService.updateStudentProgress(
        'project-1', 
        'student-1', 
        { progressPercentage: 50.0 }
      )).rejects.toThrow('Student progress not found');
    });
  });
});
