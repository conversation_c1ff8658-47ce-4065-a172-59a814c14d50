import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import enhancedGradesService from '../../../src/services/enhancedGradesService.js';
import { Grade, Submission, User, Project, Course, Checkpoint, CheckpointProgress, StudentProjectProgress } from '../../../src/models/associations.js';

// Mock the models
vi.mock('../../../src/models/associations.js', () => ({
  Grade: {
    findAndCountAll: vi.fn(),
    findAll: vi.fn(),
    count: vi.fn(),
    sequelize: {
      fn: vi.fn(),
      col: vi.fn(),
      literal: vi.fn()
        .mockReturnValueOnce('AVG(percentage)')
        .mockReturnValueOnce('MIN(percentage)')
        .mockReturnValueOnce('MAX(percentage)')
        .mockReturnValueOnce('COUNT(id)')
        .mockReturnValueOnce('CASE WHEN percentage >= 90 THEN \'A\' WHEN percentage >= 80 THEN \'B\' WHEN percentage >= 70 THEN \'C\' WHEN percentage >= 60 THEN \'D\' ELSE \'F\' END')
    }
  },
  Submission: {
    findOne: vi.fn()
  },
  User: {
    findOne: vi.fn()
  },
  Project: {
    findOne: vi.fn()
  },
  Course: {
    findOne: vi.fn()
  },
  Checkpoint: {
    findAll: vi.fn()
  },
  CheckpointProgress: {
    findAll: vi.fn()
  },
  StudentProjectProgress: {
    findOne: vi.fn()
  }
}));

// Mock logger
vi.mock('../../../src/config/logger.js', () => ({
  default: {
    error: vi.fn(),
    info: vi.fn()
  }
}));

describe('EnhancedGradesService', () => {
  const mockStudentId = 'student-123';
  const mockProjectId = 'project-456';
  const mockCourseId = 'course-789';

  const mockGrade = {
    id: 'grade-1',
    total_score: 85,
    max_score: 100,
    percentage: 85.0,
    letter_grade: 'B',
    feedback: 'Good work',
    rubric_scores: { code_quality: 8, documentation: 7 },
    graded_at: new Date('2024-01-15'),
    submission: {
      id: 'submission-1',
      submitted_at: new Date('2024-01-14'),
      status: 'graded',
      user: {
        id: mockStudentId,
        name: 'John Doe',
        email: '<EMAIL>',
        profile_picture: null
      },
      project: {
        id: mockProjectId,
        title: 'Data Analysis Project',
        description: 'Analyze sales data',
        due_date: new Date('2024-01-15'),
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-15'),
        difficulty_level: 'intermediate',
        estimated_hours: 20,
        total_points: 100,
        status: 'published',
        is_team_project: false,
        max_team_size: 1,
        learning_objectives: ['Learn data analysis', 'Practice visualization'],
        prerequisites: ['Basic Python', 'Statistics'],
        course: {
          id: mockCourseId,
          name: 'Data Science 101',
          code: 'DS101',
          term: 'Fall 2024'
        },
        rubrics: [
          {
            id: 'rubric-1',
            name: 'Code Quality',
            description: 'Code quality assessment',
            criteria: { readability: 5, efficiency: 5 },
            max_score: 10,
            weight: 0.3
          }
        ]
      }
    },
    evaluator: {
      id: 'evaluator-1',
      name: 'Dr. Smith',
      email: '<EMAIL>'
    },
    created_at: new Date('2024-01-15'),
    updated_at: new Date('2024-01-15')
  };

  const mockProjectProgress = {
    progress_percentage: 100.0,
    current_phase: 'completed',
    time_spent_hours: 25.5,
    status: 'completed',
    start_date: new Date('2024-01-01'),
    completion_date: new Date('2024-01-14'),
    last_activity: new Date('2024-01-14')
  };

  const mockCheckpoints = [
    {
      id: 'checkpoint-1',
      title: 'Data Collection',
      description: 'Collect and clean data',
      checkpoint_number: 1,
      due_date: new Date('2024-01-05'),
      weight_percentage: 20.0,
      is_required: true,
      status: 'published'
    },
    {
      id: 'checkpoint-2',
      title: 'Analysis',
      description: 'Perform data analysis',
      checkpoint_number: 2,
      due_date: new Date('2024-01-10'),
      weight_percentage: 30.0,
      is_required: true,
      status: 'published'
    }
  ];

  const mockCheckpointProgress = [
    {
      checkpoint_id: 'checkpoint-1',
      status: 'completed',
      started_at: new Date('2024-01-02'),
      submitted_at: new Date('2024-01-04'),
      completed_at: new Date('2024-01-05'),
      instructor_feedback: 'Excellent work',
      time_spent_minutes: 120
    },
    {
      checkpoint_id: 'checkpoint-2',
      status: 'completed',
      started_at: new Date('2024-01-06'),
      submitted_at: new Date('2024-01-09'),
      completed_at: new Date('2024-01-10'),
      instructor_feedback: 'Good analysis',
      time_spent_minutes: 180
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getEnhancedStudentGrades', () => {
    it('should return enhanced grades with project and checkpoint data', async () => {
      // Mock the database calls
      Grade.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: [mockGrade]
      });

      StudentProjectProgress.findOne.mockResolvedValue(mockProjectProgress);
      Checkpoint.findAll.mockResolvedValue(mockCheckpoints);
      CheckpointProgress.findAll.mockResolvedValue(mockCheckpointProgress);

      // Mock analytics
      Grade.findAll.mockResolvedValueOnce([{
        average: 78.5,
        min: 65.0,
        max: 95.0,
        count: 25
      }]);

      Grade.count.mockResolvedValue(5);
      Grade.findAll.mockResolvedValueOnce([
        { grade_band: 'A', count: 3 },
        { grade_band: 'B', count: 8 },
        { grade_band: 'C', count: 10 },
        { grade_band: 'D', count: 3 },
        { grade_band: 'F', count: 1 }
      ]);

      const result = await enhancedGradesService.getEnhancedStudentGrades(mockStudentId, {
        page: 1,
        limit: 10,
        includeAnalytics: true,
        includeCheckpoints: true
      });

      expect(result).toHaveProperty('grades');
      expect(result).toHaveProperty('pagination');
      expect(result.grades).toHaveLength(1);
      expect(result.grades[0]).toHaveProperty('submission.project.progress');
      expect(result.grades[0]).toHaveProperty('submission.project.checkpoints');
      expect(result.grades[0]).toHaveProperty('analytics');
    });

    it('should handle errors gracefully and return basic grade data', async () => {
      Grade.findAndCountAll.mockRejectedValue(new Error('Database error'));

      await expect(
        enhancedGradesService.getEnhancedStudentGrades(mockStudentId)
      ).rejects.toThrow('Database error');
    });
  });

  describe('getProjectProgressData', () => {
    it('should return project progress data when available', async () => {
      StudentProjectProgress.findOne.mockResolvedValue(mockProjectProgress);

      const result = await enhancedGradesService.getProjectProgressData(mockProjectId, mockStudentId);

      expect(result).toEqual({
        progressPercentage: 100.0,
        currentPhase: 'completed',
        timeSpentHours: 25.5,
        status: 'completed',
        startDate: mockProjectProgress.start_date,
        completionDate: mockProjectProgress.completion_date,
        lastActivity: mockProjectProgress.last_activity
      });
    });

    it('should return default values when no progress data exists', async () => {
      StudentProjectProgress.findOne.mockResolvedValue(null);

      const result = await enhancedGradesService.getProjectProgressData(mockProjectId, mockStudentId);

      expect(result).toEqual({
        progressPercentage: 0,
        currentPhase: 'not_started',
        timeSpentHours: 0,
        status: 'not_started',
        startDate: null,
        completionDate: null,
        lastActivity: null
      });
    });
  });

  describe('getCheckpointData', () => {
    it('should return checkpoint data with progress information', async () => {
      Checkpoint.findAll.mockResolvedValue(mockCheckpoints);
      CheckpointProgress.findAll.mockResolvedValue(mockCheckpointProgress);

      const result = await enhancedGradesService.getCheckpointData(mockProjectId, mockStudentId);

      expect(result).toHaveProperty('total', 2);
      expect(result).toHaveProperty('completed', 2);
      expect(result).toHaveProperty('inProgress', 0);
      expect(result).toHaveProperty('notStarted', 0);
      expect(result).toHaveProperty('overdue', 0);
      expect(result).toHaveProperty('details');
      expect(result.details).toHaveLength(2);
    });

    it('should handle missing checkpoint progress gracefully', async () => {
      Checkpoint.findAll.mockResolvedValue(mockCheckpoints);
      CheckpointProgress.findAll.mockResolvedValue([]);

      const result = await enhancedGradesService.getCheckpointData(mockProjectId, mockStudentId);

      expect(result.total).toBe(2);
      expect(result.completed).toBe(0);
      expect(result.details[0].status).toBe('not_started');
    });
  });

  describe('getGradeAnalytics', () => {
    it('should return grade analytics with class statistics', async () => {
      Grade.findAll.mockResolvedValueOnce([{
        average: 78.5,
        min: 65.0,
        max: 95.0,
        count: 25
      }]);

      Grade.count.mockResolvedValue(5);
      Grade.findAll.mockResolvedValueOnce([
        { grade_band: 'A', count: 3 },
        { grade_band: 'B', count: 8 },
        { grade_band: 'C', count: 10 },
        { grade_band: 'D', count: 3 },
        { grade_band: 'F', count: 1 }
      ]);

      const result = await enhancedGradesService.getGradeAnalytics(mockGrade, mockProjectId);

      expect(result).toHaveProperty('classAverage', 78.5);
      expect(result).toHaveProperty('minGrade', 65.0);
      expect(result).toHaveProperty('maxGrade', 95.0);
      expect(result).toHaveProperty('totalStudents', 25);
      expect(result).toHaveProperty('percentile');
      expect(result).toHaveProperty('gradeDistribution');
      expect(result).toHaveProperty('trend');
    });
  });

  describe('calculateTrend', () => {
    it('should return correct trend indicators', () => {
      expect(enhancedGradesService.calculateTrend(95, 80)).toBe('excellent');
      expect(enhancedGradesService.calculateTrend(85, 80)).toBe('above_average');
      expect(enhancedGradesService.calculateTrend(80, 80)).toBe('average');
      expect(enhancedGradesService.calculateTrend(75, 80)).toBe('below_average');
      expect(enhancedGradesService.calculateTrend(65, 80)).toBe('needs_improvement');
    });
  });
});
