import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import app from '../../src/server.js';

// Mock authentication
const mockAuth = {
  user: {
    id: 'student-123',
    email: '<EMAIL>',
    role: 'student',
    name: '<PERSON>'
  },
  userRoles: ['student']
};

// Mock database responses
const mockGradeData = {
  id: 'grade-1',
  total_score: 85,
  max_score: 100,
  percentage: 85.0,
  letter_grade: 'B',
  feedback: 'Good work on the data analysis project',
  rubric_scores: {
    code_quality: 8,
    documentation: 7,
    analysis: 9
  },
  graded_at: new Date('2024-01-15T10:30:00Z'),
  submission: {
    id: 'submission-1',
    submitted_at: new Date('2024-01-14T15:45:00Z'),
    status: 'graded',
    user: {
      id: 'student-123',
      name: '<PERSON>',
      email: '<EMAIL>',
      profile_picture: null
    },
    project: {
      id: 'project-456',
      title: 'Data Analysis Project',
      description: 'Analyze sales data and create visualizations',
      due_date: new Date('2024-01-15T23:59:59Z'),
      start_date: new Date('2024-01-01T00:00:00Z'),
      end_date: new Date('2024-01-15T23:59:59Z'),
      difficulty_level: 'intermediate',
      estimated_hours: 20,
      total_points: 100,
      status: 'published',
      is_team_project: false,
      max_team_size: 1,
      learning_objectives: [
        'Learn data analysis techniques',
        'Practice data visualization',
        'Understand statistical concepts'
      ],
      prerequisites: [
        'Basic Python programming',
        'Statistics fundamentals'
      ],
      course: {
        id: 'course-789',
        name: 'Data Science 101',
        code: 'DS101',
        term: 'Fall 2024'
      },
      rubrics: [
        {
          id: 'rubric-1',
          name: 'Code Quality',
          description: 'Assessment of code quality and structure',
          criteria: {
            readability: 5,
            efficiency: 5
          },
          max_score: 10,
          weight: 0.3
        }
      ]
    }
  },
  evaluator: {
    id: 'evaluator-1',
    name: 'Dr. Smith',
    email: '<EMAIL>'
  },
  created_at: new Date('2024-01-15T10:30:00Z'),
  updated_at: new Date('2024-01-15T10:30:00Z')
};

const mockProjectProgress = {
  progress_percentage: 100.0,
  current_phase: 'completed',
  time_spent_hours: 25.5,
  status: 'completed',
  start_date: new Date('2024-01-01T00:00:00Z'),
  completion_date: new Date('2024-01-14T15:45:00Z'),
  last_activity: new Date('2024-01-14T15:45:00Z')
};

const mockCheckpoints = [
  {
    id: 'checkpoint-1',
    title: 'Data Collection',
    description: 'Collect and clean the sales data',
    checkpoint_number: 1,
    due_date: new Date('2024-01-05T23:59:59Z'),
    weight_percentage: 20.0,
    is_required: true,
    status: 'published'
  },
  {
    id: 'checkpoint-2',
    title: 'Data Analysis',
    description: 'Perform statistical analysis on the data',
    checkpoint_number: 2,
    due_date: new Date('2024-01-10T23:59:59Z'),
    weight_percentage: 30.0,
    is_required: true,
    status: 'published'
  },
  {
    id: 'checkpoint-3',
    title: 'Visualization',
    description: 'Create charts and graphs',
    checkpoint_number: 3,
    due_date: new Date('2024-01-15T23:59:59Z'),
    weight_percentage: 25.0,
    is_required: true,
    status: 'published'
  }
];

const mockCheckpointProgress = [
  {
    checkpoint_id: 'checkpoint-1',
    status: 'completed',
    started_at: new Date('2024-01-02T09:00:00Z'),
    submitted_at: new Date('2024-01-04T14:30:00Z'),
    completed_at: new Date('2024-01-05T10:15:00Z'),
    instructor_feedback: 'Excellent data collection and cleaning work',
    time_spent_minutes: 120
  },
  {
    checkpoint_id: 'checkpoint-2',
    status: 'completed',
    started_at: new Date('2024-01-06T10:00:00Z'),
    submitted_at: new Date('2024-01-09T16:45:00Z'),
    completed_at: new Date('2024-01-10T11:30:00Z'),
    instructor_feedback: 'Good statistical analysis, well-reasoned conclusions',
    time_spent_minutes: 180
  },
  {
    checkpoint_id: 'checkpoint-3',
    status: 'completed',
    started_at: new Date('2024-01-11T08:30:00Z'),
    submitted_at: new Date('2024-01-14T15:45:00Z'),
    completed_at: new Date('2024-01-15T09:20:00Z'),
    instructor_feedback: 'Creative visualizations, clear and informative',
    time_spent_minutes: 150
  }
];

describe('Student Grades E2E Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Enhanced Grades API', () => {
    it('should return enhanced grades with project-wise and checkpoint data', async () => {
      // Mock the enhanced grades service response
      const mockEnhancedResponse = {
        grades: [{
          id: 'grade-1',
          totalScore: 85,
          maxScore: 100,
          percentage: 85.0,
          letterGrade: 'B',
          feedback: 'Good work on the data analysis project',
          rubricScores: {
            code_quality: 8,
            documentation: 7,
            analysis: 9
          },
          gradedAt: '2024-01-15T10:30:00Z',
          submission: {
            id: 'submission-1',
            submittedAt: '2024-01-14T15:45:00Z',
            status: 'graded',
            user: {
              id: 'student-123',
              name: 'John Doe',
              email: '<EMAIL>',
              profilePicture: null
            },
            project: {
              id: 'project-456',
              title: 'Data Analysis Project',
              description: 'Analyze sales data and create visualizations',
              dueDate: '2024-01-15T23:59:59Z',
              startDate: '2024-01-01T00:00:00Z',
              endDate: '2024-01-15T23:59:59Z',
              difficultyLevel: 'intermediate',
              estimatedHours: 20,
              totalPoints: 100,
              status: 'published',
              isTeamProject: false,
              maxTeamSize: 1,
              learningObjectives: [
                'Learn data analysis techniques',
                'Practice data visualization',
                'Understand statistical concepts'
              ],
              prerequisites: [
                'Basic Python programming',
                'Statistics fundamentals'
              ],
              course: {
                id: 'course-789',
                name: 'Data Science 101',
                code: 'DS101',
                term: 'Fall 2024'
              },
              rubrics: [
                {
                  id: 'rubric-1',
                  name: 'Code Quality',
                  description: 'Assessment of code quality and structure',
                  criteria: {
                    readability: 5,
                    efficiency: 5
                  },
                  maxScore: 10,
                  weight: 0.3
                }
              ],
              progress: {
                progressPercentage: 100.0,
                currentPhase: 'completed',
                timeSpentHours: 25.5,
                status: 'completed',
                startDate: '2024-01-01T00:00:00Z',
                completionDate: '2024-01-14T15:45:00Z',
                lastActivity: '2024-01-14T15:45:00Z'
              },
              checkpoints: {
                total: 3,
                completed: 3,
                inProgress: 0,
                notStarted: 0,
                overdue: 0,
                details: [
                  {
                    id: 'checkpoint-1',
                    title: 'Data Collection',
                    description: 'Collect and clean the sales data',
                    checkpointNumber: 1,
                    dueDate: '2024-01-05T23:59:59Z',
                    weightPercentage: 20.0,
                    isRequired: true,
                    status: 'completed',
                    startedAt: '2024-01-02T09:00:00Z',
                    submittedAt: '2024-01-04T14:30:00Z',
                    completedAt: '2024-01-05T10:15:00Z',
                    instructorFeedback: 'Excellent data collection and cleaning work',
                    timeSpentMinutes: 120
                  },
                  {
                    id: 'checkpoint-2',
                    title: 'Data Analysis',
                    description: 'Perform statistical analysis on the data',
                    checkpointNumber: 2,
                    dueDate: '2024-01-10T23:59:59Z',
                    weightPercentage: 30.0,
                    isRequired: true,
                    status: 'completed',
                    startedAt: '2024-01-06T10:00:00Z',
                    submittedAt: '2024-01-09T16:45:00Z',
                    completedAt: '2024-01-10T11:30:00Z',
                    instructorFeedback: 'Good statistical analysis, well-reasoned conclusions',
                    timeSpentMinutes: 180
                  },
                  {
                    id: 'checkpoint-3',
                    title: 'Visualization',
                    description: 'Create charts and graphs',
                    checkpointNumber: 3,
                    dueDate: '2024-01-15T23:59:59Z',
                    weightPercentage: 25.0,
                    isRequired: true,
                    status: 'completed',
                    startedAt: '2024-01-11T08:30:00Z',
                    submittedAt: '2024-01-14T15:45:00Z',
                    completedAt: '2024-01-15T09:20:00Z',
                    instructorFeedback: 'Creative visualizations, clear and informative',
                    timeSpentMinutes: 150
                  }
                ]
              }
            }
          },
          evaluator: {
            id: 'evaluator-1',
            name: 'Dr. Smith',
            email: '<EMAIL>'
          },
          analytics: {
            classAverage: 78.5,
            minGrade: 65.0,
            maxGrade: 95.0,
            totalStudents: 25,
            percentile: 75,
            gradeDistribution: {
              A: 3,
              B: 8,
              C: 10,
              D: 3,
              F: 1
            },
            trend: 'above_average'
          },
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        }],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 10
        }
      };

      // Mock the service
      vi.doMock('../../src/services/enhancedGradesService.js', () => ({
        default: {
          getEnhancedStudentGrades: vi.fn().mockResolvedValue(mockEnhancedResponse)
        }
      }));

      const response = await request(app)
        .get('/api/grades?enhanced=true')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      // Verify response structure
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('grades');
      expect(response.body.data).toHaveProperty('pagination');

      // Verify enhanced grade data
      const grade = response.body.data.grades[0];
      expect(grade).toHaveProperty('submission.project.progress');
      expect(grade).toHaveProperty('submission.project.checkpoints');
      expect(grade).toHaveProperty('analytics');

      // Verify project progress data
      expect(grade.submission.project.progress).toHaveProperty('progressPercentage', 100.0);
      expect(grade.submission.project.progress).toHaveProperty('status', 'completed');
      expect(grade.submission.project.progress).toHaveProperty('timeSpentHours', 25.5);

      // Verify checkpoint data
      expect(grade.submission.project.checkpoints).toHaveProperty('total', 3);
      expect(grade.submission.project.checkpoints).toHaveProperty('completed', 3);
      expect(grade.submission.project.checkpoints).toHaveProperty('details');
      expect(grade.submission.project.checkpoints.details).toHaveLength(3);

      // Verify analytics
      expect(grade.analytics).toHaveProperty('classAverage', 78.5);
      expect(grade.analytics).toHaveProperty('percentile', 75);
      expect(grade.analytics).toHaveProperty('trend', 'above_average');
    });

    it('should return student grade summary', async () => {
      const mockSummaryResponse = {
        summary: {
          totalGrades: 5,
          totalPoints: 425,
          maxPoints: 500,
          averagePercentage: 85.0,
          gradeDistribution: {
            A: 1,
            B: 3,
            C: 1,
            D: 0,
            F: 0
          }
        },
        recentGrades: [
          {
            id: 'grade-1',
            projectTitle: 'Data Analysis Project',
            courseName: 'Data Science 101',
            score: 85,
            maxScore: 100,
            percentage: 85.0,
            letterGrade: 'B',
            gradedAt: '2024-01-15T10:30:00Z'
          }
        ]
      };

      const response = await request(app)
        .get('/api/grades/student/summary')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('recentGrades');
    });

    it('should return project-specific grade details', async () => {
      const response = await request(app)
        .get('/api/grades/project/project-456')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('submission');
      expect(response.body.data.submission).toHaveProperty('project');
    });

    it('should return checkpoint grades for a project', async () => {
      const response = await request(app)
        .get('/api/grades/checkpoints/project-456')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('completed');
      expect(response.body.data).toHaveProperty('details');
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      const response = await request(app)
        .get('/api/grades')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle invalid project ID', async () => {
      const response = await request(app)
        .get('/api/grades/project/invalid-id')
        .set('Authorization', 'Bearer valid-token')
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle non-existent project', async () => {
      const response = await request(app)
        .get('/api/grades/project/00000000-0000-0000-0000-000000000000')
        .set('Authorization', 'Bearer valid-token')
        .expect(404);

      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/grades?limit=50')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds
      expect(response.body).toHaveProperty('success', true);
    });
  });
});
