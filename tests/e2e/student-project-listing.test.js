import { jest } from '@jest/globals';
import request from 'supertest';
import app from '../../src/server.js';

describe('Student Project Listing - End-to-End Tests', () => {
  let studentToken;
  let instructorToken;
  let testProjectId;
  let testCourseId;

  beforeAll(async () => {
    // Setup test data and authentication tokens
    // This would typically involve creating test users, courses, and projects
    studentToken = 'test-student-token';
    instructorToken = 'test-instructor-token';
    testProjectId = 'test-project-id';
    testCourseId = 'test-course-id';
  });

  describe('Student Project Listing Flow', () => {
    it('should complete the full student project listing workflow', async () => {
      // Step 1: Get projects list with enhanced student data
      const projectsResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(projectsResponse.body.success).toBe(true);
      expect(projectsResponse.body.data.projects).toBeDefined();
      expect(projectsResponse.body.data.pagination).toBeDefined();

      const project = projectsResponse.body.data.projects[0];
      
      // Verify enhanced student data is present
      expect(project.studentProgress).toBeDefined();
      expect(project.studentProgress.progressPercentage).toBeDefined();
      expect(project.studentProgress.status).toBeDefined();
      expect(project.studentProgress.timeSpentHours).toBeDefined();
      
      expect(project.checkpoints).toBeDefined();
      expect(project.checkpoints.total).toBeDefined();
      expect(project.checkpoints.completed).toBeDefined();
      expect(project.checkpoints.details).toBeDefined();
      
      expect(project.timeline).toBeDefined();
      expect(project.timeline.duration).toBeDefined();
      expect(project.timeline.daysRemaining).toBeDefined();
      expect(project.timeline.progressStatus).toBeDefined();

      // Step 2: Get student project statistics
      const statsResponse = await request(app)
        .get('/api/projects/student/stats')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(statsResponse.body.success).toBe(true);
      expect(statsResponse.body.data.totalProjects).toBeDefined();
      expect(statsResponse.body.data.averageProgress).toBeDefined();
      expect(statsResponse.body.data.totalTimeSpent).toBeDefined();

      // Step 3: Update student progress
      const progressUpdate = {
        progressPercentage: 75.0,
        currentPhase: 'Testing',
        timeSpentHours: 15.0,
        status: 'in_progress'
      };

      const updateResponse = await request(app)
        .put(`/api/projects/${testProjectId}/student-progress`)
        .set('Authorization', `Bearer ${studentToken}`)
        .send(progressUpdate)
        .expect(200);

      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.data.progress.progressPercentage).toBe(75.0);

      // Step 4: Verify updated progress in projects list
      const updatedProjectsResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      const updatedProject = updatedProjectsResponse.body.data.projects.find(
        p => p.id === testProjectId
      );
      
      expect(updatedProject.studentProgress.progressPercentage).toBe(75.0);
      expect(updatedProject.studentProgress.currentPhase).toBe('Testing');
    });

    it('should handle course-specific project filtering', async () => {
      // Get projects for specific course
      const courseProjectsResponse = await request(app)
        .get(`/api/projects?courseId=${testCourseId}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(courseProjectsResponse.body.success).toBe(true);
      
      // Verify all projects belong to the specified course
      courseProjectsResponse.body.data.projects.forEach(project => {
        expect(project.course.id).toBe(testCourseId);
      });

      // Get course-specific statistics
      const courseStatsResponse = await request(app)
        .get(`/api/projects/student/stats?courseId=${testCourseId}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(courseStatsResponse.body.success).toBe(true);
      expect(courseStatsResponse.body.data.totalProjects).toBeGreaterThanOrEqual(0);
    });

    it('should handle pagination correctly', async () => {
      // Test first page
      const page1Response = await request(app)
        .get('/api/projects?page=1&limit=2')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(page1Response.body.data.pagination.currentPage).toBe(1);
      expect(page1Response.body.data.pagination.itemsPerPage).toBe(2);
      expect(page1Response.body.data.projects.length).toBeLessThanOrEqual(2);

      // Test second page if available
      if (page1Response.body.data.pagination.totalPages > 1) {
        const page2Response = await request(app)
          .get('/api/projects?page=2&limit=2')
          .set('Authorization', `Bearer ${studentToken}`)
          .expect(200);

        expect(page2Response.body.data.pagination.currentPage).toBe(2);
        expect(page2Response.body.data.projects.length).toBeLessThanOrEqual(2);
      }
    });

    it('should handle search functionality', async () => {
      const searchResponse = await request(app)
        .get('/api/projects?search=machine')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(searchResponse.body.success).toBe(true);
      
      // Verify search results contain the search term
      searchResponse.body.data.projects.forEach(project => {
        const titleMatch = project.title.toLowerCase().includes('machine');
        const descMatch = project.description.toLowerCase().includes('machine');
        expect(titleMatch || descMatch).toBe(true);
      });
    });

    it('should handle different project statuses', async () => {
      // Test filtering by status
      const publishedResponse = await request(app)
        .get('/api/projects?status=published')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(publishedResponse.body.success).toBe(true);
      
      publishedResponse.body.data.projects.forEach(project => {
        expect(project.status).toBe('published');
      });
    });

    it('should handle difficulty level filtering', async () => {
      const difficultyResponse = await request(app)
        .get('/api/projects?difficulty=intermediate')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(difficultyResponse.body.success).toBe(true);
      
      difficultyResponse.body.data.projects.forEach(project => {
        expect(project.difficultyLevel).toBe('intermediate');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid project ID in progress update', async () => {
      const progressUpdate = {
        progressPercentage: 50.0
      };

      const response = await request(app)
        .put('/api/projects/invalid-id/student-progress')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(progressUpdate)
        .expect(400);

      expect(response.body.errors).toBeDefined();
    });

    it('should handle unauthorized access', async () => {
      const response = await request(app)
        .get('/api/projects/student/stats')
        .expect(401);

      expect(response.body.error).toBeDefined();
    });

    it('should handle invalid progress data', async () => {
      const invalidProgress = {
        progressPercentage: 150.0, // Invalid: > 100
        status: 'invalid_status'
      };

      const response = await request(app)
        .put(`/api/projects/${testProjectId}/student-progress`)
        .set('Authorization', `Bearer ${studentToken}`)
        .send(invalidProgress)
        .expect(400);

      expect(response.body.errors).toBeDefined();
    });

    it('should handle non-student access to student endpoints', async () => {
      const response = await request(app)
        .get('/api/projects/student/stats')
        .set('Authorization', `Bearer ${instructorToken}`)
        .expect(403);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('Performance Tests', () => {
    it('should respond within acceptable time limits', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds
      expect(response.body.success).toBe(true);
    });

    it('should handle large datasets efficiently', async () => {
      // Test with larger limit
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/projects?limit=50')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(3000); // Should respond within 3 seconds
      expect(response.body.data.projects.length).toBeLessThanOrEqual(50);
    });
  });

  describe('Data Consistency', () => {
    it('should maintain data consistency across multiple requests', async () => {
      // Get initial project data
      const initialResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      const initialProject = initialResponse.body.data.projects[0];
      const initialProgress = initialProject.studentProgress.progressPercentage;

      // Update progress
      const progressUpdate = {
        progressPercentage: initialProgress + 10
      };

      await request(app)
        .put(`/api/projects/${initialProject.id}/student-progress`)
        .set('Authorization', `Bearer ${studentToken}`)
        .send(progressUpdate)
        .expect(200);

      // Verify updated progress
      const updatedResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      const updatedProject = updatedResponse.body.data.projects.find(
        p => p.id === initialProject.id
      );

      expect(updatedProject.studentProgress.progressPercentage).toBe(initialProgress + 10);
    });

    it('should handle concurrent progress updates', async () => {
      const projectId = testProjectId;
      
      // Make multiple concurrent updates
      const updatePromises = [
        request(app)
          .put(`/api/projects/${projectId}/student-progress`)
          .set('Authorization', `Bearer ${studentToken}`)
          .send({ progressPercentage: 60 }),
        request(app)
          .put(`/api/projects/${projectId}/student-progress`)
          .set('Authorization', `Bearer ${studentToken}`)
          .send({ progressPercentage: 70 }),
        request(app)
          .put(`/api/projects/${projectId}/student-progress`)
          .set('Authorization', `Bearer ${studentToken}`)
          .send({ progressPercentage: 80 })
      ];

      const responses = await Promise.all(updatePromises);
      
      // All updates should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });
  });
});
