import { jest } from '@jest/globals';
import request from 'supertest';
import app from '../../../src/server.js';
import { Project, Course, User, StudentProjectProgress, Checkpoint, CheckpointProgress } from '../../../src/models/associations.js';

// Mock the models
jest.mock('../../../src/models/associations.js', () => ({
  Project: {
    findAndCountAll: jest.fn(),
    findByPk: jest.fn()
  },
  Course: {},
  User: {},
  StudentProjectProgress: {
    findOne: jest.fn(),
    create: jest.fn()
  },
  Checkpoint: {
    findAll: jest.fn()
  },
  CheckpointProgress: {},
  Submission: {
    count: jest.fn(),
    findOne: jest.fn()
  }
}));

// Mock services
jest.mock('../../../src/services/studentProjectService.js', () => ({
  getStudentProjectData: jest.fn(),
  getStudentProjectStats: jest.fn(),
  updateStudentProgress: jest.fn()
}));

import studentProjectService from '../../../src/services/studentProjectService.js';

describe('Project Controller - Student Features', () => {
  const mockStudentUser = {
    id: 'student-1',
    name: 'John Student',
    email: '<EMAIL>',
    roles: ['student']
  };

  const mockInstructorUser = {
    id: 'instructor-1',
    name: 'Dr. Smith',
    email: '<EMAIL>',
    roles: ['instructor']
  };

  const mockProject = {
    id: 'project-1',
    title: 'Machine Learning Project',
    description: 'Build a predictive model',
    status: 'published',
    difficulty_level: 'intermediate',
    estimated_hours: 20,
    due_date: new Date('2024-02-28T23:59:59Z'),
    start_date: new Date('2024-01-01T00:00:00Z'),
    end_date: new Date('2024-03-01T23:59:59Z'),
    created_at: new Date('2024-01-01T00:00:00Z'),
    updated_at: new Date('2024-01-15T10:30:00Z'),
    course: {
      id: 'course-1',
      name: 'Data Science 101',
      code: 'DS101',
      term: 'Fall 2024',
      instructor: {
        id: 'instructor-1',
        name: 'Dr. Smith',
        email: '<EMAIL>'
      }
    },
    creator: {
      id: 'instructor-1',
      name: 'Dr. Smith',
      email: '<EMAIL>'
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/projects - Student Enhanced Response', () => {
    it('should return enhanced project data for students', async () => {
      // Mock project query
      Project.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: [mockProject]
      });

      // Mock submission data
      const { Submission } = require('../../../src/models/associations.js');
      Submission.count.mockResolvedValue(5);
      Submission.findOne.mockResolvedValue({
        id: 'submission-1',
        status: 'submitted',
        submitted_at: new Date('2024-02-25T10:30:00Z'),
        grade: 85.5
      });

      // Mock student project service
      const mockStudentData = {
        studentProgress: {
          progressPercentage: 65.5,
          currentPhase: 'Implementation',
          timeSpentHours: 13.2,
          lastActivity: new Date('2024-01-15T10:30:00Z'),
          status: 'in_progress',
          enrollmentDate: new Date('2024-01-01T00:00:00Z'),
          startDate: new Date('2024-01-02T09:00:00Z'),
          completionDate: null,
          grade: null,
          feedback: null
        },
        checkpoints: {
          total: 5,
          completed: 3,
          inProgress: 1,
          notStarted: 1,
          overdue: 0,
          details: [
            {
              id: 'checkpoint-1',
              title: 'Project Setup',
              checkpointNumber: 1,
              dueDate: new Date('2024-01-10T23:59:59Z'),
              weightPercentage: 20.0,
              isRequired: true,
              status: 'completed',
              submittedAt: new Date('2024-01-08T14:30:00Z'),
              grade: 95.0,
              feedback: 'Excellent work!'
            }
          ]
        },
        timeline: {
          duration: 60,
          daysRemaining: 15,
          isOverdue: false,
          progressStatus: 'on_track',
          startDate: new Date('2024-01-01T00:00:00Z'),
          endDate: new Date('2024-03-01T23:59:59Z'),
          dueDate: new Date('2024-02-28T23:59:59Z')
        }
      };

      studentProjectService.getStudentProjectData.mockResolvedValue(mockStudentData);

      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', 'Bearer valid-student-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      
      const project = response.body.data.projects[0];
      expect(project.id).toBe('project-1');
      expect(project.title).toBe('Machine Learning Project');
      expect(project.endDate).toBeDefined(); // NEW: end date
      
      // Student-specific data
      expect(project.studentProgress).toBeDefined();
      expect(project.studentProgress.progressPercentage).toBe(65.5);
      expect(project.studentProgress.status).toBe('in_progress');
      
      expect(project.checkpoints).toBeDefined();
      expect(project.checkpoints.total).toBe(5);
      expect(project.checkpoints.completed).toBe(3);
      
      expect(project.timeline).toBeDefined();
      expect(project.timeline.duration).toBe(60);
      expect(project.timeline.progressStatus).toBe('on_track');

      expect(studentProjectService.getStudentProjectData).toHaveBeenCalledWith(
        mockProject, 
        'student-1'
      );
    });

    it('should return basic project data for instructors', async () => {
      // Mock project query
      Project.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: [mockProject]
      });

      // Mock submission data
      const { Submission } = require('../../../src/models/associations.js');
      Submission.count.mockResolvedValue(5);
      Submission.findOne.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', 'Bearer valid-instructor-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      
      const project = response.body.data.projects[0];
      expect(project.id).toBe('project-1');
      expect(project.title).toBe('Machine Learning Project');
      expect(project.endDate).toBeDefined(); // NEW: end date
      
      // Should not have student-specific data
      expect(project.studentProgress).toBeUndefined();
      expect(project.checkpoints).toBeUndefined();
      expect(project.timeline).toBeUndefined();

      expect(studentProjectService.getStudentProjectData).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully when student data fails', async () => {
      // Mock project query
      Project.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: [mockProject]
      });

      // Mock submission data
      const { Submission } = require('../../../src/models/associations.js');
      Submission.count.mockResolvedValue(5);
      Submission.findOne.mockResolvedValue(null);

      // Mock student service error
      studentProjectService.getStudentProjectData.mockRejectedValue(
        new Error('Student data error')
      );

      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', 'Bearer valid-student-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      
      const project = response.body.data.projects[0];
      expect(project.id).toBe('project-1');
      expect(project.title).toBe('Machine Learning Project');
      
      // Should fall back to basic project data
      expect(project.studentProgress).toBeUndefined();
      expect(project.checkpoints).toBeUndefined();
      expect(project.timeline).toBeUndefined();
    });
  });

  describe('GET /api/projects/student/stats', () => {
    it('should return student project statistics', async () => {
      const mockStats = {
        totalProjects: 5,
        notStarted: 1,
        inProgress: 2,
        completed: 2,
        overdue: 0,
        averageProgress: 65.5,
        totalTimeSpent: 67.5,
        averageGrade: 82.3
      };

      studentProjectService.getStudentProjectStats.mockResolvedValue(mockStats);

      const response = await request(app)
        .get('/api/projects/student/stats')
        .set('Authorization', 'Bearer valid-student-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Student project statistics retrieved successfully');
      expect(response.body.data).toEqual(mockStats);

      expect(studentProjectService.getStudentProjectStats).toHaveBeenCalledWith(
        'student-1', 
        undefined
      );
    });

    it('should filter by course when provided', async () => {
      const mockStats = {
        totalProjects: 3,
        notStarted: 0,
        inProgress: 1,
        completed: 2,
        overdue: 0,
        averageProgress: 80.0,
        totalTimeSpent: 45.0,
        averageGrade: 85.0
      };

      studentProjectService.getStudentProjectStats.mockResolvedValue(mockStats);

      const response = await request(app)
        .get('/api/projects/student/stats?courseId=course-1')
        .set('Authorization', 'Bearer valid-student-token')
        .expect(200);

      expect(studentProjectService.getStudentProjectStats).toHaveBeenCalledWith(
        'student-1', 
        'course-1'
      );
    });

    it('should return 500 on service error', async () => {
      studentProjectService.getStudentProjectStats.mockRejectedValue(
        new Error('Service error')
      );

      const response = await request(app)
        .get('/api/projects/student/stats')
        .set('Authorization', 'Bearer valid-student-token')
        .expect(500);

      expect(response.body.error).toBe('Internal Server Error');
      expect(response.body.message).toBe('Failed to retrieve student project statistics');
    });
  });

  describe('PUT /api/projects/:id/student-progress', () => {
    it('should update student progress successfully', async () => {
      const mockUpdatedProgress = {
        progress_percentage: 75.0,
        current_phase: 'Testing',
        time_spent_hours: 15.0,
        status: 'in_progress',
        last_activity: new Date('2024-01-15T10:30:00Z')
      };

      // Mock project access check
      Project.findByPk.mockResolvedValue({
        id: 'project-1',
        course: {
          enrollments: [{ user_id: 'student-1' }]
        }
      });

      studentProjectService.updateStudentProgress.mockResolvedValue(mockUpdatedProgress);

      const progressData = {
        progressPercentage: 75.0,
        currentPhase: 'Testing',
        timeSpentHours: 15.0,
        status: 'in_progress'
      };

      const response = await request(app)
        .put('/api/projects/project-1/student-progress')
        .set('Authorization', 'Bearer valid-student-token')
        .send(progressData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Student progress updated successfully');
      expect(response.body.data.projectId).toBe('project-1');
      expect(response.body.data.studentId).toBe('student-1');
      expect(response.body.data.progress.progressPercentage).toBe(75.0);

      expect(studentProjectService.updateStudentProgress).toHaveBeenCalledWith(
        'project-1',
        'student-1',
        progressData
      );
    });

    it('should return 404 when project not found or access denied', async () => {
      Project.findByPk.mockResolvedValue(null);

      const progressData = {
        progressPercentage: 50.0
      };

      const response = await request(app)
        .put('/api/projects/project-1/student-progress')
        .set('Authorization', 'Bearer valid-student-token')
        .send(progressData)
        .expect(404);

      expect(response.body.error).toBe('Not Found');
      expect(response.body.message).toBe('Project not found or access denied');
    });

    it('should return 404 when student progress not found', async () => {
      // Mock project access check
      Project.findByPk.mockResolvedValue({
        id: 'project-1',
        course: {
          enrollments: [{ user_id: 'student-1' }]
        }
      });

      studentProjectService.updateStudentProgress.mockRejectedValue(
        new Error('Student progress not found')
      );

      const progressData = {
        progressPercentage: 50.0
      };

      const response = await request(app)
        .put('/api/projects/project-1/student-progress')
        .set('Authorization', 'Bearer valid-student-token')
        .send(progressData)
        .expect(404);

      expect(response.body.error).toBe('Not Found');
      expect(response.body.message).toBe('Student progress not found');
    });

    it('should validate request body', async () => {
      const invalidData = {
        progressPercentage: 150.0, // Invalid: > 100
        status: 'invalid_status'
      };

      const response = await request(app)
        .put('/api/projects/project-1/student-progress')
        .set('Authorization', 'Bearer valid-student-token')
        .send(invalidData)
        .expect(400);

      expect(response.body.errors).toBeDefined();
    });
  });
});
