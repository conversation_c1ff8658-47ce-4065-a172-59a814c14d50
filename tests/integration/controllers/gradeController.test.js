import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import app from '../../../src/server.js';
import { Grade, Submission, User, Project, Course, Checkpoint, CheckpointProgress, StudentProjectProgress } from '../../../src/models/associations.js';

// Mock authentication middleware
vi.mock('../../../src/middlewares/auth.js', () => ({
  jwtMiddleware: (req, res, next) => {
    req.user = {
      id: 'student-123',
      email: '<EMAIL>',
      role: 'student',
      name: '<PERSON>'
    };
    req.userRoles = ['student'];
    next();
  }
}));

// Mock RBAC middleware
vi.mock('../../../src/middlewares/rbac.js', () => ({
  requirePermissions: () => (req, res, next) => next(),
  requireRoles: () => (req, res, next) => next()
}));

// Mock validation middleware
vi.mock('../../../src/middlewares/validation.js', () => ({
  validate: (req, res, next) => next()
}));

describe('Enhanced Grade Controller Integration Tests', () => {
  const mockStudentId = 'student-123';
  const mockProjectId = 'project-456';
  const mockCourseId = 'course-789';

  const mockGrade = {
    id: 'grade-1',
    total_score: 85,
    max_score: 100,
    percentage: 85.0,
    letter_grade: 'B',
    feedback: 'Good work',
    rubric_scores: { code_quality: 8, documentation: 7 },
    graded_at: new Date('2024-01-15'),
    submission: {
      id: 'submission-1',
      submitted_at: new Date('2024-01-14'),
      status: 'graded',
      user: {
        id: mockStudentId,
        name: 'John Doe',
        email: '<EMAIL>',
        profile_picture: null
      },
      project: {
        id: mockProjectId,
        title: 'Data Analysis Project',
        description: 'Analyze sales data',
        due_date: new Date('2024-01-15'),
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-15'),
        difficulty_level: 'intermediate',
        estimated_hours: 20,
        total_points: 100,
        status: 'published',
        is_team_project: false,
        max_team_size: 1,
        learning_objectives: ['Learn data analysis', 'Practice visualization'],
        prerequisites: ['Basic Python', 'Statistics'],
        course: {
          id: mockCourseId,
          name: 'Data Science 101',
          code: 'DS101',
          term: 'Fall 2024'
        },
        rubrics: []
      }
    },
    evaluator: {
      id: 'evaluator-1',
      name: 'Dr. Smith',
      email: '<EMAIL>'
    },
    created_at: new Date('2024-01-15'),
    updated_at: new Date('2024-01-15')
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /api/grades (Enhanced)', () => {
    it('should return enhanced grades for students with project and checkpoint data', async () => {
      // Mock the enhanced grades service
      const mockEnhancedData = {
        grades: [{
          id: 'grade-1',
          totalScore: 85,
          maxScore: 100,
          percentage: 85.0,
          letterGrade: 'B',
          feedback: 'Good work',
          submission: {
            project: {
              id: mockProjectId,
              title: 'Data Analysis Project',
              totalPoints: 100,
              difficultyLevel: 'intermediate',
              progress: {
                progressPercentage: 100.0,
                currentPhase: 'completed',
                timeSpentHours: 25.5,
                status: 'completed'
              },
              checkpoints: {
                total: 2,
                completed: 2,
                inProgress: 0,
                notStarted: 0,
                overdue: 0,
                details: []
              }
            }
          },
          analytics: {
            classAverage: 78.5,
            percentile: 75,
            trend: 'above_average'
          }
        }],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 10
        }
      };

      // Mock the enhanced grades service
      vi.doMock('../../../src/services/enhancedGradesService.js', () => ({
        default: {
          getEnhancedStudentGrades: vi.fn().mockResolvedValue(mockEnhancedData)
        }
      }));

      const response = await request(app)
        .get('/api/grades?enhanced=true')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('grades');
      expect(response.body.data).toHaveProperty('pagination');
    });

    it('should fall back to basic grades if enhanced service fails', async () => {
      // Mock the enhanced grades service to throw an error
      vi.doMock('../../../src/services/enhancedGradesService.js', () => ({
        default: {
          getEnhancedStudentGrades: vi.fn().mockRejectedValue(new Error('Service error'))
        }
      }));

      // Mock the basic grade query
      Grade.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: [mockGrade]
      });

      const response = await request(app)
        .get('/api/grades?enhanced=true')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });
  });

  describe('GET /api/grades/student/summary', () => {
    it('should return student grade summary', async () => {
      const mockGrades = [
        { ...mockGrade, percentage: 85 },
        { ...mockGrade, id: 'grade-2', percentage: 92 },
        { ...mockGrade, id: 'grade-3', percentage: 78 }
      ];

      Grade.findAll.mockResolvedValue(mockGrades);

      const response = await request(app)
        .get('/api/grades/student/summary')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('recentGrades');
      expect(response.body.data.summary).toHaveProperty('totalGrades', 3);
      expect(response.body.data.summary).toHaveProperty('averagePercentage');
      expect(response.body.data.summary).toHaveProperty('gradeDistribution');
    });

    it('should filter by course when courseId is provided', async () => {
      Grade.findAll.mockResolvedValue([mockGrade]);

      const response = await request(app)
        .get('/api/grades/student/summary?courseId=course-789')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
    });
  });

  describe('GET /api/grades/project/:projectId', () => {
    it('should return detailed project grade information', async () => {
      const mockEnhancedData = {
        grades: [{
          id: 'grade-1',
          totalScore: 85,
          maxScore: 100,
          percentage: 85.0,
          submission: {
            project: {
              id: mockProjectId,
              title: 'Data Analysis Project',
              progress: {
                progressPercentage: 100.0,
                status: 'completed'
              },
              checkpoints: {
                total: 2,
                completed: 2,
                details: []
              }
            }
          },
          analytics: {
            classAverage: 78.5,
            percentile: 75
          }
        }]
      };

      vi.doMock('../../../src/services/enhancedGradesService.js', () => ({
        default: {
          getEnhancedStudentGrades: vi.fn().mockResolvedValue(mockEnhancedData)
        }
      }));

      const response = await request(app)
        .get(`/api/grades/project/${mockProjectId}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('submission');
      expect(response.body.data.submission).toHaveProperty('project');
    });

    it('should return 404 when no grades found for project', async () => {
      vi.doMock('../../../src/services/enhancedGradesService.js', () => ({
        default: {
          getEnhancedStudentGrades: vi.fn().mockResolvedValue({ grades: [] })
        }
      }));

      const response = await request(app)
        .get(`/api/grades/project/${mockProjectId}`)
        .expect(404);

      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /api/grades/checkpoints/:projectId', () => {
    it('should return checkpoint grades for a project', async () => {
      const mockCheckpointData = {
        total: 2,
        completed: 2,
        inProgress: 0,
        notStarted: 0,
        overdue: 0,
        details: [
          {
            id: 'checkpoint-1',
            title: 'Data Collection',
            checkpointNumber: 1,
            status: 'completed',
            weightPercentage: 20.0
          },
          {
            id: 'checkpoint-2',
            title: 'Analysis',
            checkpointNumber: 2,
            status: 'completed',
            weightPercentage: 30.0
          }
        ]
      };

      vi.doMock('../../../src/services/enhancedGradesService.js', () => ({
        default: {
          getCheckpointData: vi.fn().mockResolvedValue(mockCheckpointData)
        }
      }));

      const response = await request(app)
        .get(`/api/grades/checkpoints/${mockProjectId}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('total', 2);
      expect(response.body.data).toHaveProperty('completed', 2);
      expect(response.body.data).toHaveProperty('details');
      expect(response.body.data.details).toHaveLength(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      Grade.findAll.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/grades/student/summary')
        .expect(500);

      expect(response.body).toHaveProperty('error', 'Internal Server Error');
    });

    it('should handle invalid project ID format', async () => {
      const response = await request(app)
        .get('/api/grades/project/invalid-id')
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });
  });
});
