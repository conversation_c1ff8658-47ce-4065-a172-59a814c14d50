import { jest } from '@jest/globals';
import { sequelize } from '../../src/config/database.js';
import { 
  User, 
  Course, 
  Project, 
  StudentProjectProgress, 
  Checkpoint, 
  CheckpointProgress,
  CourseEnrollment 
} from '../../src/models/associations.js';

/**
 * Test setup utilities for student project listing tests
 */
export class StudentProjectTestSetup {
  
  /**
   * Create test users with different roles
   */
  static async createTestUsers() {
    const testUsers = [
      {
        id: 'test-student-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'student',
        is_active: true
      },
      {
        id: 'test-instructor-1',
        name: 'Dr<PERSON>',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'instructor',
        is_active: true
      },
      {
        id: 'test-admin-1',
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'admin',
        is_active: true
      }
    ];

    const createdUsers = [];
    for (const userData of testUsers) {
      const user = await User.create(userData);
      createdUsers.push(user);
    }

    return createdUsers;
  }

  /**
   * Create test courses
   */
  static async createTestCourses() {
    const testCourses = [
      {
        id: 'test-course-1',
        name: 'Data Science 101',
        code: 'DS101',
        description: 'Introduction to Data Science',
        term: 'Fall 2024',
        instructor_id: 'test-instructor-1',
        is_active: true
      },
      {
        id: 'test-course-2',
        name: 'Machine Learning',
        code: 'ML201',
        description: 'Advanced Machine Learning',
        term: 'Fall 2024',
        instructor_id: 'test-instructor-1',
        is_active: true
      }
    ];

    const createdCourses = [];
    for (const courseData of testCourses) {
      const course = await Course.create(courseData);
      createdCourses.push(course);
    }

    return createdCourses;
  }

  /**
   * Create test projects
   */
  static async createTestProjects() {
    const testProjects = [
      {
        id: 'test-project-1',
        title: 'Machine Learning Project',
        description: 'Build a predictive model using machine learning',
        course_id: 'test-course-1',
        created_by: 'test-instructor-1',
        status: 'published',
        difficulty_level: 'intermediate',
        estimated_hours: 20,
        start_date: new Date('2024-01-01T00:00:00Z'),
        end_date: new Date('2024-03-01T23:59:59Z'),
        due_date: new Date('2024-02-28T23:59:59Z'),
        instructions: 'Complete the project following the guidelines'
      },
      {
        id: 'test-project-2',
        title: 'Data Analysis Project',
        description: 'Analyze dataset and create visualizations',
        course_id: 'test-course-1',
        created_by: 'test-instructor-1',
        status: 'published',
        difficulty_level: 'beginner',
        estimated_hours: 15,
        start_date: new Date('2024-01-15T00:00:00Z'),
        end_date: new Date('2024-02-15T23:59:59Z'),
        due_date: new Date('2024-02-14T23:59:59Z'),
        instructions: 'Focus on data cleaning and visualization'
      },
      {
        id: 'test-project-3',
        title: 'Deep Learning Project',
        description: 'Implement neural networks for image classification',
        course_id: 'test-course-2',
        created_by: 'test-instructor-1',
        status: 'draft',
        difficulty_level: 'advanced',
        estimated_hours: 30,
        start_date: new Date('2024-02-01T00:00:00Z'),
        end_date: new Date('2024-04-01T23:59:59Z'),
        due_date: new Date('2024-03-31T23:59:59Z'),
        instructions: 'Use TensorFlow or PyTorch'
      }
    ];

    const createdProjects = [];
    for (const projectData of testProjects) {
      const project = await Project.create(projectData);
      createdProjects.push(project);
    }

    return createdProjects;
  }

  /**
   * Create test course enrollments
   */
  static async createTestEnrollments() {
    const testEnrollments = [
      {
        user_id: 'test-student-1',
        course_id: 'test-course-1',
        enrollment_date: new Date('2024-01-01T00:00:00Z'),
        status: 'active'
      },
      {
        user_id: 'test-student-1',
        course_id: 'test-course-2',
        enrollment_date: new Date('2024-01-01T00:00:00Z'),
        status: 'active'
      }
    ];

    const createdEnrollments = [];
    for (const enrollmentData of testEnrollments) {
      const enrollment = await CourseEnrollment.create(enrollmentData);
      createdEnrollments.push(enrollment);
    }

    return createdEnrollments;
  }

  /**
   * Create test checkpoints
   */
  static async createTestCheckpoints() {
    const testCheckpoints = [
      {
        id: 'test-checkpoint-1',
        project_id: 'test-project-1',
        title: 'Project Setup',
        description: 'Set up the development environment',
        checkpoint_number: 1,
        due_date: new Date('2024-01-10T23:59:59Z'),
        weight_percentage: 20.0,
        is_required: true,
        status: 'published',
        created_by: 'test-instructor-1'
      },
      {
        id: 'test-checkpoint-2',
        project_id: 'test-project-1',
        title: 'Data Preprocessing',
        description: 'Clean and preprocess the dataset',
        checkpoint_number: 2,
        due_date: new Date('2024-01-20T23:59:59Z'),
        weight_percentage: 25.0,
        is_required: true,
        status: 'published',
        created_by: 'test-instructor-1'
      },
      {
        id: 'test-checkpoint-3',
        project_id: 'test-project-1',
        title: 'Model Implementation',
        description: 'Implement the machine learning model',
        checkpoint_number: 3,
        due_date: new Date('2024-02-10T23:59:59Z'),
        weight_percentage: 30.0,
        is_required: true,
        status: 'published',
        created_by: 'test-instructor-1'
      },
      {
        id: 'test-checkpoint-4',
        project_id: 'test-project-1',
        title: 'Testing and Evaluation',
        description: 'Test the model and evaluate performance',
        checkpoint_number: 4,
        due_date: new Date('2024-02-20T23:59:59Z'),
        weight_percentage: 25.0,
        is_required: true,
        status: 'published',
        created_by: 'test-instructor-1'
      }
    ];

    const createdCheckpoints = [];
    for (const checkpointData of testCheckpoints) {
      const checkpoint = await Checkpoint.create(checkpointData);
      createdCheckpoints.push(checkpoint);
    }

    return createdCheckpoints;
  }

  /**
   * Create test student project progress
   */
  static async createTestStudentProgress() {
    const testProgress = [
      {
        student_id: 'test-student-1',
        project_id: 'test-project-1',
        course_id: 'test-course-1',
        enrollment_date: new Date('2024-01-01T00:00:00Z'),
        start_date: new Date('2024-01-02T09:00:00Z'),
        progress_percentage: 65.5,
        current_phase: 'Model Implementation',
        time_spent_hours: 13.2,
        last_activity: new Date('2024-01-15T10:30:00Z'),
        status: 'in_progress'
      },
      {
        student_id: 'test-student-1',
        project_id: 'test-project-2',
        course_id: 'test-course-1',
        enrollment_date: new Date('2024-01-01T00:00:00Z'),
        start_date: new Date('2024-01-16T09:00:00Z'),
        progress_percentage: 100.0,
        current_phase: 'Completed',
        time_spent_hours: 15.0,
        last_activity: new Date('2024-02-10T16:45:00Z'),
        status: 'completed',
        completion_date: new Date('2024-02-10T16:45:00Z'),
        grade: 88.5,
        feedback: 'Excellent work on data visualization!'
      }
    ];

    const createdProgress = [];
    for (const progressData of testProgress) {
      const progress = await StudentProjectProgress.create(progressData);
      createdProgress.push(progress);
    }

    return createdProgress;
  }

  /**
   * Create test checkpoint progress
   */
  static async createTestCheckpointProgress() {
    const testCheckpointProgress = [
      {
        checkpoint_id: 'test-checkpoint-1',
        user_id: 'test-student-1',
        project_id: 'test-project-1',
        status: 'completed',
        started_at: new Date('2024-01-02T09:00:00Z'),
        submitted_at: new Date('2024-01-08T14:30:00Z'),
        completed_at: new Date('2024-01-08T14:30:00Z'),
        time_spent_minutes: 480,
        last_activity: new Date('2024-01-08T14:30:00Z')
      },
      {
        checkpoint_id: 'test-checkpoint-2',
        user_id: 'test-student-1',
        project_id: 'test-project-1',
        status: 'completed',
        started_at: new Date('2024-01-09T10:00:00Z'),
        submitted_at: new Date('2024-01-15T16:20:00Z'),
        completed_at: new Date('2024-01-15T16:20:00Z'),
        time_spent_minutes: 600,
        last_activity: new Date('2024-01-15T16:20:00Z')
      },
      {
        checkpoint_id: 'test-checkpoint-3',
        user_id: 'test-student-1',
        project_id: 'test-project-1',
        status: 'in_progress',
        started_at: new Date('2024-01-16T09:00:00Z'),
        submitted_at: null,
        completed_at: null,
        time_spent_minutes: 240,
        last_activity: new Date('2024-01-15T10:30:00Z')
      },
      {
        checkpoint_id: 'test-checkpoint-4',
        user_id: 'test-student-1',
        project_id: 'test-project-1',
        status: 'not_started',
        started_at: null,
        submitted_at: null,
        completed_at: null,
        time_spent_minutes: 0,
        last_activity: null
      }
    ];

    const createdCheckpointProgress = [];
    for (const progressData of testCheckpointProgress) {
      const progress = await CheckpointProgress.create(progressData);
      createdCheckpointProgress.push(progress);
    }

    return createdCheckpointProgress;
  }

  /**
   * Setup complete test environment
   */
  static async setupTestEnvironment() {
    try {
      // Create test data in order
      const users = await this.createTestUsers();
      const courses = await this.createTestCourses();
      const projects = await this.createTestProjects();
      const enrollments = await this.createTestEnrollments();
      const checkpoints = await this.createTestCheckpoints();
      const studentProgress = await this.createTestStudentProgress();
      const checkpointProgress = await this.createTestCheckpointProgress();

      return {
        users,
        courses,
        projects,
        enrollments,
        checkpoints,
        studentProgress,
        checkpointProgress
      };
    } catch (error) {
      console.error('Error setting up test environment:', error);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  static async cleanupTestEnvironment() {
    try {
      // Delete in reverse order to handle foreign key constraints
      await CheckpointProgress.destroy({ where: {}, force: true });
      await StudentProjectProgress.destroy({ where: {}, force: true });
      await Checkpoint.destroy({ where: {}, force: true });
      await CourseEnrollment.destroy({ where: {}, force: true });
      await Project.destroy({ where: {}, force: true });
      await Course.destroy({ where: {}, force: true });
      await User.destroy({ where: {}, force: true });
    } catch (error) {
      console.error('Error cleaning up test environment:', error);
      throw error;
    }
  }

  /**
   * Generate test authentication tokens
   */
  static generateTestTokens() {
    return {
      studentToken: 'test-student-token',
      instructorToken: 'test-instructor-token',
      adminToken: 'test-admin-token'
    };
  }
}

export default StudentProjectTestSetup;
