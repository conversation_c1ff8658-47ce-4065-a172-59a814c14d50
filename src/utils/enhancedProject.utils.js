import logger from '../config/logger.config.js';
import { Project, LtiContext, User, Checkpoint, Rubric } from '../models/associations.js';
import ApiError from './ApiError.utils.js';
import httpStatus from 'http-status';
import s3Service from '../services/s3.service.js';

class enhancedProjectUtils {
  /**
   * check if project is exits or not
   */
  async checkProjectExist(projectId) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, is_deleted: false }
      });
      if (!project) {
        return false;
      }
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update project details
   */
  async updateProjectDetails(projectId, updates, transaction = null) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, is_deleted: false },
        transaction
      });
      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      await project.update(updates, { transaction });
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update a project's dataset S3 URLs
   * @param {string|string[]} s3Urls - The S3 URL(s) of the dataset
   * @param {string} projectId - The ID of the project to update
   * @returns {Promise<void>}
   */
  async updateDataSetS3Url(s3Urls, projectId) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, is_deleted: false }
      });
      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }

      // Ensure s3Urls is always an array
      const urlsArray = Array.isArray(s3Urls) ? s3Urls : [s3Urls];

      if (!project.dataset_s3_url || !Array.isArray(project.dataset_s3_url)) {
        project.dataset_s3_url = urlsArray;
      } else {
        project.dataset_s3_url = project.dataset_s3_url.concat(urlsArray);
      }

      await project.save();
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a project belongs to a specific course
   * @param {UUID} courseId
   * @param {UUID} projectId
   * @returns
   */
  async checkCourseProjectId(courseId, projectId) {
    try {
      const where = {
        id: projectId,
        is_deleted: false
      };
      if (courseId) {
        where.course_id = courseId;
      }
      const project = await Project.findOne({
        where
      });
      if (!project) {
        if (courseId) {
          logger.error(
            `Project ${projectId} does not belong to course ${courseId}`
          );
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Project does not belong to the specified course'
          );
        }
        logger.error(`Project ${projectId} not found`);
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get complete project details with all relationships and S3 details
   * @param {string} projectId - Project ID
   * @returns {Object} Complete project details
   */
  async getProjectWithCompleteDetails(projectId) {
    try {
      const projectWithDetails = await Project.findOne({
        where: { id: projectId, is_deleted: false },
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            required: false
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Checkpoint,
            as: 'checkpoints',
            required: false,
            attributes: [
              'id',
              'title',
              'description',
              'checkpoint_number',
              'start_date',
              'due_date',
              'status',
              'is_required'
            ],
            include: [
              {
                model: Rubric,
                as: 'rubrics',
                required: false,
                attributes: [
                  'id',
                  'title',
                  'description',
                  'criteria',
                  'total_points',
                  'is_template',
                  'template_name',
                  'created_at'
                ],
                include: [
                  {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'name', 'email']
                  }
                ]
              }
            ]
          }
        ],
        order: [
          [{ model: Checkpoint, as: 'checkpoints' }, 'checkpoint_number', 'ASC']
        ]
      });

      if (!projectWithDetails) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      // Process S3 dataset URLs
      let datasetS3Details = [];
      if (
        Array.isArray(projectWithDetails.dataset_s3_url) &&
        projectWithDetails.dataset_s3_url.length > 0
      ) {
        try {
          datasetS3Details = await Promise.all(
            projectWithDetails.dataset_s3_url.map(async key => {
              try {
                return await s3Service.generatePresignedDownloadUrl(
                  key,
                  3600,
                  true
                );
              } catch (err) {
                logger.warn(`Could not fetch S3 details for key: ${key}`);
                return { key, error: 'Not found or inaccessible' };
              }
            })
          );
        } catch (error) {
          logger.warn('Error processing S3 dataset URLs:', error);
          datasetS3Details = [];
        }
      }

      return {
        ...projectWithDetails.toJSON(),
        dataset_s3_url: datasetS3Details,
        instructors: [],
        teachingAssistants: [],
        students: [],
        Submission: { count: 0, rows: [] }
      };
    } catch (error) {
      throw error;
    }
  }
}

export default new enhancedProjectUtils();
