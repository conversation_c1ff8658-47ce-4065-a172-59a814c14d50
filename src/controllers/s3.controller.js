import httpStatus from 'http-status';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import S3Service from '../services/s3.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';

let component, auditComponent;
/**
 * @desc    Upload project dataset file
 * @route   POST /api/s3/upload-project-dataset
 * @access  Private (Instructor/Admin)
 * @form    multipart/form-data with field name: dataset
 */
export const uploadProjectDataset = asyncHandler(
  async (req, res) => {
    component = 'uploadProjectDataset';
    auditComponent = 'Upload Project Dataset';
    const result = await S3Service.uploadProjectDataset(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Successfully Upload Project Dataset File',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);


export const uploadUserFileToProject = asyncHandler(
  async (req, res) => {
    const component = 'uploadUserFileToProject';
    const auditComponent = 'Upload User File to Project';
    
    // Call the new service function
    const result = await S3Service.uploadUserFile(req);

    await buildSuccessResponse(
      req,
      res,
      result,
      'Successfully uploaded files to project directory',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component: 'uploadUserFileToProject', auditComponent: 'Upload User File to Project' }
);
