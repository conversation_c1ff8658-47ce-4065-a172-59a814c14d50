import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import enhancedProjectService from '../services/enhancedProject.service.js';
import courseRoleService from '../services/courseRole.service.js';
import logger from '../config/logger.config.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

let component, auditComponent;
/**
 * @desc    Create/Update enhanced project with assignments and template support
 * @route   POST /api/projects/enhanced
 * @access  Private (Instructor/Admin)
 */
export const createEnhancedProject = asyncHandler(
  async (req, res) => {
    component = 'createEnhancedProject';
    auditComponent = 'Create Enhanced Project';
    const result = await enhancedProjectService.creationOfProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      `${req.body.isScreen > 1 || req.body.id ? 'Project updated successfully' : 'Project created successfully'}`,
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get enhanced projects
 * @route   GET /api/projects/enhanced
 * @access  Private (Instructor/Admin/TA/Student)
 */
export const getEnhancedProject = asyncHandler(
  async (req, res) => {
    component = 'getEnhancedProject';
    auditComponent = 'Fetch Enhanced Project';
    const result = await enhancedProjectService.getEnhancedProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Fetch Enhanced project successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get project with full details
 * @route   GET /api/projects/:id/details
 * @access  Private
 */
export const getProjectWithDetails = asyncHandler(
  async (req, res) => {
    component = 'getProjectWithDetails';
    auditComponent = 'Fetch Enhanced Project with Details';
    const result = await enhancedProjectService.getProjectWithDetails(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Fetch Enhanced project with details successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Publish project
 * @route   POST /api/projects/:id/publish
 * @access  Private (Instructor/Admin)
 */
export const publishProject = asyncHandler(
  async (req, res) => {
    component = 'publishProject';
    auditComponent = 'Publish Project';
    const result = await enhancedProjectService.publishProject(
      req.params.id,
      req.user.id,
      req.userRoles
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project published successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Unpublish project
 * @route   POST /api/projects/:id/unpublish
 * @access  Private (Instructor/Admin)
 */
export const unpublishProject = asyncHandler(
  async (req, res) => {
    component = 'unpublishProject';
    auditComponent = 'Unpublish Project';
    const result = await enhancedProjectService.unpublishProject(
      req.params.id,
      req.user.id,
      req.userRoles
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project unpublished successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Duplicate project
 * @route   POST /api/projects/:id/duplicate
 * @access  Private (Instructor, Admin)
 */
export const duplicateProject = asyncHandler(
  async (req, res) => {
    component = 'duplicateProject';
    auditComponent = 'Duplicate Project';
    const result = await enhancedProjectService.duplicateProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project duplicated successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Delete project
 * @route   DELETE /api/projects/:id
 * @access  Private (Instructor, Admin)
 */
export const deleteProject = asyncHandler(
  async (req, res) => {
    component = 'deleteProject';
    auditComponent = 'Delete Project';
    const result = await enhancedProjectService.deleteProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project deleted successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Create project template
 * @route   POST /api/projects/templates
 * @access  Private (Instructor/Admin)
 */
export const createProjectTemplate = asyncHandler(
  async (req, res) => {
    component = 'createProjectTemplate';
    auditComponent = 'Create Project Template';
    const result = await enhancedProjectService.createProjectTemplate(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project template created successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Create template from existing project
 * @route   POST /api/projects/:id/create-template
 * @access  Private (Creator, Admin)
 */
export const createTemplateFromProject = asyncHandler(
  async (req, res) => {
    component = 'createTemplateFromProject';
    auditComponent = 'Create Template From Project';
    const result = await enhancedProjectService.createTemplateFromProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Template created successfully from project',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Update project template
 * @route   PUT /api/projects/templates/:id
 * @access  Private (Template Creator)
 */
export const updateProjectTemplate = asyncHandler(
  async (req, res) => {
    component = 'updateProjectTemplate';
    auditComponent = 'Update Project Template';
    const result = await enhancedProjectService.updateProjectTemplate(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Template updated successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get project templates
 * @route   GET /api/projects/templates
 * @access  Private
 */
export const getProjectTemplates = asyncHandler(
  async (req, res) => {
    component = 'getProjectTemplates';
    auditComponent = 'Fetch Project Templates';
    const result = await enhancedProjectService.getProjectTemplates(req.query);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project templates fetched successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get template by ID
 * @route   GET /api/projects/templates/:id
 * @access  Private
 */
export const getTemplateById = asyncHandler(
  async (req, res) => {
    component = 'getTemplateById';
    auditComponent = 'Fetch Template Details';
    const result = await enhancedProjectService.getTemplateById(req.params.id);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Template details fetched successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Duplicate project from template
 * @route   POST /api/projects/templates/:id/duplicate
 * @access  Private (Instructor/Admin)
 */
export const duplicateProjectFromTemplate = asyncHandler(
  async (req, res) => {
    component = 'duplicateProjectFromTemplate';
    auditComponent = 'Duplicate Project From Template';
    const result = await enhancedProjectService.duplicateProjectFromTemplate(
      req.params.id,
      { ...req.body, created_by: req.user.id },
      req.user.id
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project duplicated from template successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Rate project template
 * @route   POST /api/projects/templates/:id/rate
 * @access  Private
 */
export const rateProjectTemplate = asyncHandler(
  async (req, res) => {
    component = 'rateProjectTemplate';
    auditComponent = 'Rate Project Template';
    const result = await enhancedProjectService.rateProjectTemplate(
      req.params.id,
      req.body.rating,
      req.user.id
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Template rated successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Delete project template
 * @route   DELETE /api/projects/templates/:id
 * @access  Private (Template Creator/Admin)
 */
export const deleteProjectTemplate = asyncHandler(
  async (req, res) => {
    component = 'deleteProjectTemplate';
    auditComponent = 'Delete Project Template';
    const result = await enhancedProjectService.deleteProjectTemplate(
      req.params.id,
      req.user.id
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Template deleted successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Feature/Unfeature project template
 * @route   PUT /api/projects/templates/:id/feature
 * @access  Private (Admin)
 */
export const featureProjectTemplate = asyncHandler(
  async (req, res) => {
    component = 'featureProjectTemplate';
    auditComponent = 'Feature Project Template';
    const result = await enhancedProjectService.featureProjectTemplate(
      req.params.id,
      req.body.featured,
      req.user.id
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      `Template ${req.body.featured ? 'featured' : 'unfeatured'} successfully`,
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Assign users to project
 * @route   POST /api/projects/:id/assignments
 * @access  Private (Instructor/Admin)
 */
export const assignUsersToProject = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;
  const { assignments } = req.body;

  if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Assignments array is required'
    });
  }

  try {
    const createdAssignments =
      await enhancedProjectService.assignUsersToProject(
        projectId,
        assignments,
        req.user.id
      );

    res.status(201).json({
      success: true,
      message: `${assignments.length} users assigned to project successfully`,
      assignments: createdAssignments
    });
  } catch (error) {
    logger.error('Error assigning users to project:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to assign users to project'
    });
  }
});

/**
 * @desc    Get project assignments
 * @route   GET /api/projects/:id/assignments
 * @access  Private
 */
export const getProjectAssignments = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;

  try {
    const assignments =
      await enhancedProjectService.getProjectAssignments(projectId);

    res.json({
      success: true,
      assignments
    });
  } catch (error) {
    logger.error('Error getting project assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignments'
    });
  }
});

/**
 * @desc    Remove user assignment from project
 * @route   DELETE /api/projects/:id/assignments/:userId
 * @access  Private (Instructor/Admin)
 */
export const removeUserAssignment = asyncHandler(async (req, res) => {
  const { id: projectId, userId } = req.params;

  try {
    const result = await enhancedProjectService.removeUserAssignment(
      projectId,
      userId
    );

    res.json({
      success: true,
      message: 'User assignment removed successfully',
      data: result
    });
  } catch (error) {
    if (error.message === 'Assignment not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    logger.error('Error removing user assignment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove user assignment'
    });
  }
});

/**
 * @desc    Save project as draft
 * @route   POST /api/projects/:id/save-draft
 * @access  Private (Instructor/Admin)
 */
export const saveProjectAsDraft = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;

  try {
    const project = await enhancedProjectService.saveProjectAsDraft(
      projectId,
      req.user.id
    );

    res.json({
      success: true,
      message: 'Project saved as draft successfully',
      project: {
        id: project.id,
        title: project.title,
        status: project.status
      }
    });
  } catch (error) {
    if (error.message === 'Project not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project not found'
      });
    }

    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }

    logger.error('Error saving project as draft:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to save project as draft'
    });
  }
});

/**
 * @desc    Get projects by user assignment
 * @route   GET /api/projects/assignments/user/:role?
 * @access  Private
 */
export const getProjectsByUserAssignment = asyncHandler(async (req, res) => {
  try {
    const { role } = req.params;
    const userId = req.user.id;

    const assignments =
      await enhancedProjectService.getProjectsByUserAssignment(userId, role);

    res.json({
      success: true,
      message: 'User project assignments retrieved successfully',
      data: {
        assignments,
        total: assignments.length,
        role: role || 'all'
      }
    });
  } catch (error) {
    logger.error('Error getting user project assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user project assignments'
    });
  }
});

/**
 * @desc    Get user's project workload
 * @route   GET /api/projects/workload
 * @access  Private
 */
export const getUserProjectWorkload = asyncHandler(async (req, res) => {
  try {
    const userId = req.user.id;

    const workload =
      await enhancedProjectService.getUserProjectWorkload(userId);

    res.json({
      success: true,
      message: 'User project workload retrieved successfully',
      data: workload
    });
  } catch (error) {
    logger.error('Error getting user project workload:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user project workload'
    });
  }
});

/**
 * @desc    Get project assignment statistics
 * @route   GET /api/projects/:id/assignment-stats
 * @access  Private
 */
export const getProjectAssignmentStats = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const stats = await enhancedProjectService.getProjectAssignmentStats(id);

    res.json({
      success: true,
      message: 'Project assignment statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    logger.error('Error getting project assignment stats:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignment statistics'
    });
  }
});

/**
 * @desc    Create enhanced project with assignments and template support
 * @route   POST /api/projects/enhanced
 * @access  Private (Instructor/Admin)
 */
export const updateEnhancedProject = asyncHandler(
  async (req, res) => {
    component = 'updateEnhancedProject';
    auditComponent = 'Update Enhanced Project';
    const result = await enhancedProjectService.UpdationOfProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Enhanced project updated successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);
