import passport from 'passport';
import { User, Role, Permission } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import logger from '../config/logger.config.js';
import authService from '../services/auth.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import tokenBlacklistService from '../services/tokenBlacklistService.js';
import jupyterService from '../services/jupyterhub.service.js';
import jwtService from '../services/jwt.service.js';
import sessionCacheService from '../services/sessionCache.service.js';

let component, auditComponent;
/**
 * @desc    Google OAuth login
 * @route   GET /api/auth/google
 * @access  Public
 */
export const googleAuth = passport.authenticate('google', {
  scope: ['profile', 'email']
});

/**
 * @desc    Google OAuth callback
 * @route   GET /api/auth/google/callback
 * @access  Public
 */
export const googleCallback = asyncHandler(async (req, res, next) => {
  passport.authenticate(
    'google',
    { session: false },
    async (err, user, info) => {
      if (err) {
        logger.error('Google OAuth error:', err);
        return res.redirect(
          `${process.env.FRONTEND_URL}/login?error=oauth_error`
        );
      }

      if (!user) {
        // Handle authentication failures with specific error messages
        const errorMessage = info?.message || 'authentication_failed';
        logger.warn(`Google OAuth authentication failed: ${errorMessage}`);
        return res.redirect(
          `${process.env.FRONTEND_URL}/login?error=${encodeURIComponent(errorMessage)}`
        );
      }

      try {
        // Generate refresh token (mirroring regular login)
        const refreshToken = jwtService.generateRefreshToken(user.id);

        // Update last login
        await user.update({
          last_login: new Date()
        });

        // Create session data and cache it (mirroring regular login)
        const sessionData = {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            lms_user_id: user.lms_user_id,
            google_id: user.google_id,
            profile_picture: user.profile_picture,
            last_login: user.last_login,
            status: user.status,
            preferences: user.preferences || {},
            metadata: user.metadata || {},
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            deletedAt: user.deletedAt,
            roles: user.roles || []
          },
          sessionType: 'google_oauth',
          createdAt: new Date().toISOString()
        };

        // Cache the session data
        await sessionCacheService.setUserSession(user.id, sessionData);

        // Store refresh token in Redis
        await sessionCacheService.setRefreshToken(refreshToken, user.id);

        // Set refresh token as HttpOnly cookie (mirroring regular login)
        res.cookie('refreshToken', refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
          path: '/'
        });

        // Extract user roles and permissions for the response
        const userRoles = [];
        const userPermissions = [];

        if (user.roles) {
          user.roles.forEach(role => {
            userRoles.push(role.name);
            if (role.permissions) {
              role.permissions.forEach(permission => {
                if (!userPermissions.includes(permission.key)) {
                  userPermissions.push(permission.key);
                }
              });
            }
          });
        }

        // Redirect to frontend with access token and user info
        const redirectUrl = new URL(
          `${process.env.FRONTEND_URL}/auth/callback`
        );
        // redirectUrl.searchParams.set('token', accessToken);
        // redirectUrl.searchParams.set('userId', user.id);
        // redirectUrl.searchParams.set('name', encodeURIComponent(user.name));
        // redirectUrl.searchParams.set('email', user.email);
        // redirectUrl.searchParams.set('roles', encodeURIComponent(userRoles.join(',')));

        logger.info(
          `Google OAuth login successful for: ${user.email} with roles: ${userRoles.join(', ')}`
        );
        res.redirect(redirectUrl.toString());
      } catch (error) {
        logger.error('Google OAuth token generation error:', error);
        res.redirect(`${process.env.FRONTEND_URL}/login?error=token_error`);
      }
    }
  )(req, res, next);
});

/**
 * @desc    Login with email and password
 * @route   POST /api/auth/login
 * @access  Public
 */
export const login = asyncHandler(
  async (req, res) => {
    component = 'login';
    auditComponent = 'User login';
    const result = await authService.login(req);

    // Set refresh token as HttpOnly cookie
    res.cookie('refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/'
    });

    // Remove refresh token from response body for security
    const responseData = {
      ...result,
      refreshToken: undefined
    };

    await buildSuccessResponse(
      req,
      res,
      responseData,
      'Login successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get current user profile
 * @route   GET /api/auth/me
 * @access  Private
 */
export const getCurrentUser = asyncHandler(async (req, res) => {
  // const user = req.user;
  const session = req.session;
  const email = session.user.email;

  const user = await User.findOne({
    where: { email: email.toLowerCase() },
    include: [
      {
        model: Role,
        as: 'roles',
        include: [
          {
            model: Permission,
            as: 'permissions'
          }
        ]
      }
    ]
  });

  const userResponse = {
    id: user.id,
    name: user.name,
    email: user.email,
    profilePicture: user.profile_picture,
    lastLogin: user.last_login,
    roles:
      user.roles?.map(role => ({
        id: role.id,
        name: role.name,
        permissions: role.permissions?.map(permission => permission.key) || []
      })) || []
  };

  // const userResponse = {
  //   id: user.id,
  //   name: user.name,
  //   email: user.email,
  //   profilePicture: user.profile_picture,
  //   lastLogin: user.last_login,
  //   status: user.status,
  //   preferences: user.preferences,
  //   roles:
  //     user.roles?.map(role => ({
  //       id: role.id,
  //       name: role.name,
  //       permissions: role.permissions?.map(permission => permission.key) || []
  //     })) || []
  // };

  res.json({
    success: true,
    user: userResponse
  });
});

/**
 * @desc    Logout user
 * @route   POST /api/auth/logout
 * @access  Private
 */
export const logout = asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.id;
    const refreshToken = req.cookies?.refreshToken;

    if (userId) {
      // Remove user session from Redis
      await sessionCacheService.deleteUserSession(userId);
      logger.info(`User session removed from Redis: ${req.user.email}`);
    }

    if (refreshToken) {
      // Remove refresh token from Redis
      await sessionCacheService.deleteRefreshToken(refreshToken);
      logger.info(
        `Refresh token removed from Redis for user: ${req.user?.email}`
      );
    }

    // Clear refresh token cookie
    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/'
    });

    if (req.user) {
      logger.info(`User logged out: ${req.user.email}`);
      await jupyterService.stopServer(req.user);
    }

    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Logout error:', error);
    // Even if cleanup fails, we still want to log the user out
    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @desc    Logout from all devices
 * @route   POST /api/auth/logout-all
 * @access  Private
 */
export const logoutAllDevices = asyncHandler(async (req, res) => {
  try {
    const user = req.user;

    if (user?.id) {
      // Remove all sessions and refresh tokens for this user
      await sessionCacheService.deleteAllUserSessions(user.id);
      logger.info(`All sessions removed from Redis for user: ${user.email}`);
    }

    // Clear refresh token cookie for current device
    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/'
    });

    logger.info(`User logged out from all devices: ${user.email}`);

    res.json({
      success: true,
      message: 'Logged out from all devices successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Logout all devices error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to logout from all devices'
    });
  }
});

/**
 * @desc    Get logout statistics
 * @route   GET /api/auth/logout/stats
 * @access  Private (Admin only)
 */
export const getLogoutStats = asyncHandler(async (req, res) => {
  try {
    // Check if user has admin permissions
    const hasAdminPermission =
      req.userPermissions?.includes('admin:read') ||
      req.userRoles?.includes('admin');

    if (!hasAdminPermission) {
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        message: 'Admin permission required'
      });
    }

    const stats = await tokenBlacklistService.getBlacklistStats();

    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Get logout stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to get logout statistics'
    });
  }
});

/**
 * @desc    Refresh token using HttpOnly cookie
 * @route   POST /api/auth/refresh
 * @access  Public (uses refresh token cookie)
 */
export const refreshToken = asyncHandler(async (req, res) => {
  try {
    // Get refresh token from HttpOnly cookie
    const refreshToken = req.cookies?.refreshToken;

    if (!refreshToken) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Refresh token not found. Please login again.'
      });
    }

    // Verify refresh token
    let decoded;
    try {
      decoded = jwtService.verifyRefreshToken(refreshToken);
    } catch (error) {
      // Clear invalid refresh token cookie
      res.clearCookie('refreshToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/'
      });

      return res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      });
    }

    // Verify refresh token exists in Redis
    const userId =
      await sessionCacheService.getUserIdFromRefreshToken(refreshToken);
    if (!userId || userId !== decoded.userId) {
      // Clear invalid refresh token cookie
      res.clearCookie('refreshToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/'
      });

      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid refresh token. Please login again.'
      });
    }

    // Get user session data from Redis
    const sessionData = await sessionCacheService.getUserSession(userId);
    if (!sessionData) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Session not found. Please login again.'
      });
    }

    logger.info('[Auth Refresh] Session data roles:', {
      userId,
      userRoles:
        sessionData.user?.roles?.map(r => ({ id: r.id, name: r.name })) || [],
      sessionType: sessionData.sessionType
    });

    // Check if user account is still active
    if (sessionData.user.status !== 'active') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User account is not active'
      });
    }

    // Generate new access token
    const newAccessToken = jwtService.generateAccessToken(sessionData.user, {
      sessionType: sessionData.sessionType,
      ltiContext: sessionData.ltiContext,
      ltiResourceLink: sessionData.ltiResourceLink
    });

    // Update last accessed time in session
    await sessionCacheService.updateLastAccessed(userId);

    logger.debug(`[Auth] Access token refreshed for user: ${userId}`);

    res.json({
      success: true,
      accessToken: newAccessToken,
      expiresIn: jwtService.accessTokenExpiry,
      tokenType: 'Bearer',
      user: {
        id: sessionData.user.id,
        name: sessionData.user.name,
        email: sessionData.user.email,
        profilePicture: sessionData.user.profile_picture,
        lastLogin: sessionData.user.last_login,
        roles:
          sessionData.user.roles?.map(role => ({
            id: role.id,
            name: role.name,
            permissions:
              role.permissions?.map(permission => permission.key) || []
          })) || []
      }
    });
  } catch (error) {
    logger.error('[Auth] Refresh token error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to refresh token'
    });
  }
});

/**
 * @desc    Update user preferences
 * @route   PATCH /api/auth/preferences
 * @access  Private
 */
export const updatePreferences = asyncHandler(async (req, res) => {
  const { preferences } = req.body;
  const user = req.user;

  if (!preferences || typeof preferences !== 'object') {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Valid preferences object is required'
    });
  }

  // Update user preferences
  await user.update({
    preferences: {
      ...user.preferences,
      ...preferences
    }
  });

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    preferences: user.preferences
  });
});
