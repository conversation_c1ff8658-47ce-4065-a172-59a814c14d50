import httpStatus from 'http-status';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import submissionService from '../services/submission.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';

let component, auditComponent;

// /**
//  * @desc    Get all submissions with pagination and filtering
//  * @route   GET /api/submissions
//  * @access  Private
//  */
export const getSubmissions = asyncHandler(async (req, res) => {
  component = 'getSubmissions';
  auditComponent = 'Fetch Submissions';
  
  const result = await submissionService.getSubmissions(req);
  
  await buildSuccessResponse(
    req,
    res,
    result,
    'Submissions retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});

/**
 * @desc    Get submission by ID
 * @route   GET /api/submissions/:id
 * @access  Private
 */
export const getSubmissionById = asyncHandler(async (req, res) => {
  component = 'getSubmissionById';
  auditComponent = 'Get Submission Details';
  
  const result = await submissionService.getSubmissionById(req);
  
  await buildSuccessResponse(
    req,
    res,
    { submission: result },
    'Submission retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});
/**
 * @desc    Get all checkpoint submissions with pagination and filtering
 * @route   GET /api/submissions/checkpoints
 * @access  Private
 */
export const getAllCheckpoints = asyncHandler(async (req, res) => {
  component = 'getAllCheckpoints';
  auditComponent = 'Fetch Checkpoint Submissions';
  
  const result = await submissionService.getAllCheckpoints(req);
  
  await buildSuccessResponse(
    req,
    res,
    result,
    'Submissions retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});


export const getCheckpointProgressById = asyncHandler(async (req, res) => {
  component = 'getCheckpointProgressById';
  auditComponent = 'Get Checkpoint Progress Details';

  const result = await submissionService.getCheckpointProgressById(req);

  await buildSuccessResponse(
    req,
    res,
    { submission: result },
    'Checkpoint progress retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});


export const getSubmissionByInstructorId = asyncHandler(async (req, res) => {
  component = 'getSubmissionByInstructorId';
  auditComponent = 'Get Submission Details by Instructor';
  
  const result = await submissionService.getSubmissionByInstructorId(req);
  
  await buildSuccessResponse(
    req,
    res,
    { submission: result },
    'Submission retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});

export const getSubmissionByProjectId = asyncHandler(async (req, res) => {
  component = 'getSubmissionByProjectId';
  auditComponent = 'Get Submissions by Project ID';

  const result = await submissionService.getSubmissionByProjectId(req);

  await buildSuccessResponse(
    req,
    res,
    { 
      submissions: result.submissions,
      pagination: result.pagination,
      project: result.project,
      statistics: result.statistics,
      filters: result.filters
    },
    'Submissions retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});

export const getSubmissionByUserIdAndProjectId = asyncHandler(async (req, res) => {
  component = 'getSubmissionByUserIdAndProjectId';
  auditComponent = 'Get Submission by User ID and Project ID';

  const result = await submissionService.getSubmissionByUserIdAndProjectId(req);

  await buildSuccessResponse(
    req,
    res,
    { submission: result },
    'Submission retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});

export const createInstructorReviewSandbox = asyncHandler(async (req, res) => {
  const component = 'createInstructorReviewSandbox';
  const auditComponent = 'Create Instructor Review Sandbox';

  // 1. Call the service layer, which contains all the complex business logic.
  const result = await submissionService.createInstructorReviewSandbox(req);

  // 2. Use your custom helper to build and send the successful response.
  await buildSuccessResponse(
    req,
    res,
    result, // The data from the service
    'Instructor review sandbox created successfully.',
    component,
    auditComponent,
    httpStatus.OK // Or httpStatus.CREATED if you prefer for resource creation
  );
});


/**
 * @desc    Create or update submission
 * @route   POST /api/submissions
 * @access  Private (Student)
 */
export const createOrUpdateSubmission = asyncHandler(async (req, res) => {
  component = 'createOrUpdateSubmission';
  auditComponent = 'Create/Update Submission';
  
  const result = await submissionService.createOrUpdateSubmission(req);
  
  await buildSuccessResponse(
    req, 
    res, 
    { submission: result }, 
    'Submission saved successfully', 
    component, 
    auditComponent, 
    httpStatus.OK
  );
});

/**
 * @desc    Auto-save submission
 * @route   POST /api/submissions/autosave
 * @access  Private (Student)
 */
export const autoSaveSubmission = asyncHandler(async (req, res) => {
  component = 'autoSaveSubmission';
  auditComponent = 'Auto Save Submission';
  
  const result = await submissionService.autoSaveSubmission(req);
  
  await buildSuccessResponse(
    req,
    res,
    result,
    'Auto-save successful',
    component,
    auditComponent,
    httpStatus.OK
  );
});

/**
 * @desc    Submit assignment (finalize submission)
 * @route   POST /api/submissions/:id/submit
 * @access  Private (Student)
 */
export const submitAssignment = asyncHandler(async (req, res) => {
  component = 'submitAssignment';
  auditComponent = 'Submit Final Assignment';
  
  const result = await submissionService.submitAssignment(req);
  
  await buildSuccessResponse(
    req,
    res,
    { submission: result },
    'Assignment submitted successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});

/**
 * @desc    Download submission notebook
 * @route   GET /api/submissions/:id/download
 * @access  Private
 */
export const downloadSubmissionNotebook = asyncHandler(async (req, res) => {
  component = 'downloadSubmissionNotebook';
  auditComponent = 'Download Submission Notebook';
  
  const result = await submissionService.downloadSubmissionNotebook(req);
  
  await buildSuccessResponse(
    req,
    res,
    result,
    'Download URL generated successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});

/**
 * @desc    Get submission statistics for a project
 * @route   GET /api/submissions/project/:projectId/statistics
 * @access  Private (Instructor, Admin)
 */
export const getSubmissionStatistics = asyncHandler(async (req, res) => {
  component = 'getSubmissionStatistics';
  auditComponent = 'Get Project Submission Statistics';
  
  const result = await submissionService.getSubmissionStatistics(req);
  
  await buildSuccessResponse(
    req,
    res,
    { statistics: result },
    'Submission statistics retrieved successfully',
    component,
    auditComponent,
    httpStatus.OK
  );
});



// // export const getSubmissionById = asyncHandler(async (req, res) => {
// //   component = 'getSubmissionById';
// //   auditComponent = 'Get Submission Details';

// //   const { id } = req.params;

// //   const submission = await Submission.findByPk(id, {
// //     include: [
// //       {
// //         model: User,
// //         as: 'user',
// //         attributes: ['id', 'name', 'email', 'profile_picture']
// //       },
// //       {
// //         model: Project,
// //         as: 'project',
// //         include: [{
// //           model: Course,
// //           as: 'course',
// //           include: [{
// //             model: User,
// //             as: 'instructor',
// //             attributes: ['id', 'name', 'email']
// //           }]
// //         }]
// //       },
// //       {
// //         model: Grade,
// //         as: 'grade',
// //         include: [{
// //           model: User,
// //           as: 'evaluator',
// //           attributes: ['id', 'name', 'email']
// //         }]
// //       }
// //     ]
// //   });

// //   if (!submission) {
// //     throw new Error('Submission not found');
// //   }

// //   // Check if user has access to this submission
// //   const hasAccess = req.userRoles.includes('admin') ||
// //                    submission.user_id === req.user.id ||
// //                    submission.project.course.instructor_id === req.user.id;

// //   if (!hasAccess) {
// //     throw new Error('You do not have permission to view this submission');
// //   }

// //   // Generate presigned URL for notebook if it exists
// //   let notebookPresignedUrl = null;
// //   if (submission.notebook_s3_url) {
// //     try {
// //       notebookPresignedUrl = await S3Service.generateFilePath(submission.notebook_s3_url);
// //     } catch (error) {
// //       LoggerError(req, 'Error generating presigned URL', component, httpStatus.INTERNAL_SERVER_ERROR, error);
// //     }
// //   }

// //   const submissionResponse = {
// //     id: submission.id,
// //     status: submission.status,
// //     submittedAt: submission.submitted_at,
// //     notebookS3Url: submission.notebook_s3_url,
// //     notebookPresignedUrl,
// //     executionTime: submission.execution_time,
// //     metadata: submission.metadata,
// //     submissionSummary: submission.submission_summary,
// //     performanceMetrics: submission.performance_metrics,
// //     timeSpent: submission.time_spent,
// //     attempts: submission.attempts,
// //     fileCount: submission.file_count,
// //     currentGrade: submission.current_grade,
// //     sandboxUrl: submission.sandbox_url,
// //     testModelResults: submission.test_model_results,
// //     user: {
// //       id: submission.user.id,
// //       name: submission.user.name,
// //       email: submission.user.email,
// //       profilePicture: submission.user.profile_picture
// //     },
// //     project: {
// //       id: submission.project.id,
// //       title: submission.project.title,
// //       description: submission.project.description,
// //       dueDate: submission.project.due_date,
// //       instructions: submission.project.instructions,
// //       difficultyLevel: submission.project.difficulty_level,
// //       course: {
// //         id: submission.project.course.id,
// //         name: submission.project.course.name,
// //         code: submission.project.course.code,
// //         instructor: {
// //           id: submission.project.course.instructor.id,
// //           name: submission.project.course.instructor.name,
// //           email: submission.project.course.instructor.email
// //         }
// //       }
// //     },
// //     grade: submission.grade ? {
// //       id: submission.grade.id,
// //       totalScore: submission.grade.total_score,
// //       maxScore: submission.grade.max_score,
// //       percentage: submission.grade.percentage,
// //       letterGrade: submission.grade.letter_grade,
// //       feedback: submission.grade.feedback,
// //       rubricScores: submission.grade.rubric_scores,
// //       evaluator: {
// //         id: submission.grade.evaluator.id,
// //         name: submission.grade.evaluator.name,
// //         email: submission.grade.evaluator.email
// //       },
// //       gradedAt: submission.grade.graded_at
// //     } : null,
// //     createdAt: submission.created_at,
// //     updatedAt: submission.updated_at
// //   };

// //   await buildSuccessResponse(
// //     req,
// //     res,
// //     { submission: submissionResponse },
// //     'Submission retrieved successfully',
// //     component,
// //     auditComponent,
// //     httpStatus.OK
// //   );
// // });


// // /**
// //  * @desc    Create or update submission
// //  * @route   POST /api/submissions
// //  * @access  Private (Student)
// //  */

// // export const createOrUpdateSubmission = asyncHandler(async (req, res) => {
// //   component = 'createOrUpdateSubmission';
// //   auditComponent = 'Create/Update Submission';
// //   const result = await submissionService.createOrUpdateSubmission(req);
// //   await buildSuccessResponse(req, res, { submission: result }, 'Submission saved successfully', component, auditComponent, httpStatus.OK);
// // });


// /**
//  * @desc    Submit assignment (finalize submission)
//  * @route   POST /api/submissions/:id/submit
//  * @access  Private (Student)
//  */

// // export const submitAssignment = asyncHandler(async (req, res) => {
// //   component = 'submitAssignment';
// //   auditComponent = 'Submit Final Assignment';

// //   const { id } = req.params;

// //   const submission = await Submission.findByPk(id, {
// //     include: [{
// //       model: Project,
// //       as: 'project',
// //       include: [{
// //         model: Course,
// //         as: 'course'
// //       }]
// //     }]
// //   });

// //   if (!submission) {
// //     throw new Error('Submission not found');
// //   }

// //   // Check if user owns this submission
// //   if (submission.user_id !== req.user.id) {
// //     throw new Error('You do not have permission to submit this assignment');
// //   }

// //   // Check if submission is already submitted
// //   if (submission.status === 'submitted') {
// //     throw new Error('This assignment has already been submitted');
// //   }

// //   // Check if submission deadline has passed
// //   if (submission.project.due_date && new Date() > new Date(submission.project.due_date)) {
// //     throw new Error('The submission deadline has passed');
// //   }

// //   // Check if notebook exists
// //   if (!submission.notebook_s3_url) {
// //     throw new Error('Please upload your notebook before submitting');
// //   }

// //   // Calculate time taken and update submission metrics
// //   const timeSpent = submission.time_spent || 0;
// //   const attempts = (submission.attempts || 0) + 1;

// //   // Update submission status and metrics
// //   const updatedSubmission = await submission.update({
// //     status: 'submitted',
// //     submitted_at: new Date(),
// //     time_spent: timeSpent,
// //     attempts: attempts,
// //     metadata: {
// //       ...submission.metadata,
// //       finalSubmission: true,
// //       submissionComplete: true,
// //       lastModified: new Date()
// //     }
// //   });

// //   // Update student progress
// //   await StudentProjectProgress.update(
// //     {
// //       submission_status: 'submitted',
// //       last_activity: new Date(),
// //       completion_date: new Date()
// //     },
// //     {
// //       where: {
// //         student_id: req.user.id,
// //         project_id: submission.project_id
// //       }
// //     }
// //   );

// //   // Log student activity
// //   await StudentActivity.create({
// //     student_id: req.user.id,
// //     project_id: submission.project_id,
// //     course_id: submission.project.course.id,
// //     activity_type: 'final_submission',
// //     metadata: {
// //       submissionId: submission.id,
// //       timeSpent,
// //       attempts,
// //       submittedAt: updatedSubmission.submitted_at
// //     }
// //   });

// //   // Send notification to instructors
// //   await NotificationService.sendSubmissionNotification({
// //     type: 'assignment_submitted',
// //     projectId: submission.project_id,
// //     courseId: submission.project.course.id,
// //     submissionId: submission.id,
// //     studentId: req.user.id,
// //     instructorId: submission.project.course.instructor_id
// //   });

// //   const responseData = {
// //     id: updatedSubmission.id,
// //     status: updatedSubmission.status,
// //     submittedAt: updatedSubmission.submitted_at,
// //     timeSpent: updatedSubmission.time_spent,
// //     attempts: updatedSubmission.attempts,
// //     projectTitle: submission.project.title,
// //     courseName: submission.project.course.name
// //   };

// //   await buildSuccessResponse(
// //     req,
// //     res,
// //     { submission: responseData },
// //     'Assignment submitted successfully',
// //     component,
// //     auditComponent,
// //     httpStatus.OK
// //   );
// // });

// // /**
// //  * @desc    Download submission notebook
// //  * @route   GET /api/submissions/:id/download
// //  * @access  Private
// //  */
// // /**
// //  * @desc    Download submission notebook
// //  * @route   GET /api/submissions/:id/download
// //  * @access  Private
// //  */
// // export const downloadSubmissionNotebook = asyncHandler(async (req, res) => {
// //   component = 'downloadSubmissionNotebook';
// //   auditComponent = 'Download Submission Notebook';

// //   const { id } = req.params;

// //   const submission = await Submission.findByPk(id, {
// //     include: [{
// //       model: Project,
// //       as: 'project',
// //       include: [{
// //         model: Course,
// //         as: 'course'
// //       }]
// //     }, {
// //       model: User,
// //       as: 'user',
// //       attributes: ['id', 'name', 'email']
// //     }]
// //   });

// //   if (!submission) {
// //     throw new Error('Submission not found');
// //   }

// //   // Check if user has access to download this submission
// //   const hasAccess = req.userRoles.includes('admin') ||
// //                    submission.user_id === req.user.id ||
// //                    submission.project.course.instructor_id === req.user.id;

// //   if (!hasAccess) {
// //     throw new Error('You do not have permission to download this submission');
// //   }

// //   if (!submission.notebook_s3_url) {
// //     throw new Error('No notebook file found for this submission');
// //   }

// //   try {
// //     // Generate presigned URL for download
// //     const downloadUrl = await S3Service.generatePresignedDownloadUrl(
// //       submission.notebook_s3_url,
// //       3600 // 1 hour expiry
// //     );

// //     // Format filename
// //     const sanitizedName = submission.user.name.replace(/[^a-zA-Z0-9]/g, '_');
// //     const sanitizedTitle = submission.project.title.replace(/[^a-zA-Z0-9]/g, '_');
// //     const fileName = `${sanitizedName}_${sanitizedTitle}_${submission.id}.ipynb`;

// //     // Log download activity
// //     await StudentActivity.create({
// //       student_id: submission.user_id,
// //       project_id: submission.project_id,
// //       course_id: submission.project.course.id,
// //       activity_type: 'notebook_download',
// //       metadata: {
// //         downloadedBy: req.user.id,
// //         downloadedByRole: req.userRoles[0],
// //         submissionId: submission.id,
// //         timestamp: new Date()
// //       }
// //     });

// //     // Update submission metadata
// //     await submission.update({
// //       metadata: {
// //         ...submission.metadata,
// //         lastDownloaded: new Date(),
// //         downloadCount: (submission.metadata?.downloadCount || 0) + 1,
// //         lastDownloadedBy: req.user.id
// //       }
// //     });

// //     const responseData = {
// //       downloadUrl,
// //       fileName,
// //       expiresIn: 3600,
// //       submission: {
// //         id: submission.id,
// //         status: submission.status,
// //         projectTitle: submission.project.title,
// //         courseName: submission.project.course.name,
// //         studentName: submission.user.name,
// //         submittedAt: submission.submitted_at
// //       }
// //     };

// //     await buildSuccessResponse(
// //       req,
// //       res,
// //       responseData,
// //       'Download URL generated successfully',
// //       component,
// //       auditComponent,
// //       httpStatus.OK
// //     );

// //   } catch (error) {
// //     LoggerError(req, 'Error generating download URL', component, httpStatus.INTERNAL_SERVER_ERROR, error);
// //     throw new Error('Failed to generate download URL');
// //   }
// // });

// // /**
// //  * @desc    Get submission statistics for a project
// //  * @route   GET /api/submissions/project/:projectId/statistics
// //  * @access  Private (Instructor, Admin)
// //  */
// // /**
// //  * @desc    Get submission statistics for a project
// //  * @route   GET /api/submissions/statistics/:projectId
// //  * @access  Private (Admin/Instructor)
// //  */
// // export const getSubmissionStatistics = asyncHandler(async (req, res) => {
// //   component = 'getSubmissionStatistics';
// //   auditComponent = 'Get Project Submission Statistics';

// //   const { projectId } = req.params;

// //   const project = await Project.findByPk(projectId, {
// //     include: [{
// //       model: Course,
// //       as: 'course'
// //     }]
// //   });

// //   if (!project) {
// //     throw new Error('Project not found');
// //   }

// //   // Check permissions
// //   const hasPermission = req.userRoles.includes('admin') ||
// //                        project.course.instructor_id === req.user.id;

// //   if (!hasPermission) {
// //     throw new Error('You do not have permission to view submission statistics');
// //   }

// //   // Get total enrolled students
// //   const totalEnrolled = await CourseEnrollment.count({
// //     where: { 
// //       course_id: project.course_id,
// //       role_in_course: 'student',
// //       enrollment_status: 'active'
// //     }
// //   });

// //   // Get submission counts by status
// //   const submissionCounts = await Submission.findAll({
// //     where: { project_id: projectId },
// //     attributes: [
// //       'status',
// //       [sequelize.fn('COUNT', sequelize.col('id')), 'count']
// //     ],
// //     group: ['status']
// //   });

// //   // Get total submissions
// //   const totalSubmissions = await Submission.count({
// //     where: { project_id: projectId }
// //   });

// //   // Get on-time vs late submissions
// //   const onTimeSubmissions = await Submission.count({
// //     where: {
// //       project_id: projectId,
// //       status: 'submitted',
// //       submitted_at: {
// //         [Op.lte]: project.due_date
// //       }
// //     }
// //   });

// //   const lateSubmissions = await Submission.count({
// //     where: {
// //       project_id: projectId,
// //       status: 'submitted',
// //       submitted_at: {
// //         [Op.gt]: project.due_date
// //       }
// //     }
// //   });

// //   // Get average metrics
// //   const averageMetrics = await Submission.findOne({
// //     where: {
// //       project_id: projectId,
// //       execution_time: { [Op.not]: null }
// //     },
// //     attributes: [
// //       [sequelize.fn('AVG', sequelize.col('execution_time')), 'avg_execution_time'],
// //       [sequelize.fn('AVG', sequelize.col('time_spent')), 'avg_time_spent'],
// //       [sequelize.fn('AVG', sequelize.col('attempts')), 'avg_attempts']
// //     ]
// //   });

// //   // Get grade distribution
// //   const gradeDistribution = await Grade.findAll({
// //     include: [{
// //       model: Submission,
// //       as: 'submission',
// //       where: { project_id: projectId }
// //     }],
// //     attributes: [
// //       [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
// //       [sequelize.fn('AVG', sequelize.col('percentage')), 'average_score']
// //     ],
// //     group: ['letter_grade']
// //   });

// //   const statistics = {
// //     overview: {
// //       totalEnrolled,
// //       totalSubmissions,
// //       submissionRate: totalEnrolled > 0 ? (totalSubmissions / totalEnrolled * 100).toFixed(1) : 0,
// //       completionRate: totalEnrolled > 0 ?
// //         (onTimeSubmissions / totalEnrolled * 100).toFixed(1) : 0
// //     },
// //     submissionStatus: submissionCounts.map(item => ({
// //       status: item.status,
// //       count: parseInt(item.getDataValue('count')),
// //       percentage: totalSubmissions > 0 ?
// //         (parseInt(item.getDataValue('count')) / totalSubmissions * 100).toFixed(1) : 0
// //     })),
// //     timeliness: {
// //       onTime: onTimeSubmissions,
// //       late: lateSubmissions,
// //       notSubmitted: totalEnrolled - totalSubmissions,
// //       onTimeRate: totalSubmissions > 0 ?
// //         (onTimeSubmissions / totalSubmissions * 100).toFixed(1) : 0
// //     },
// //     performance: {
// //       averageExecutionTime: averageMetrics ?
// //         parseFloat(averageMetrics.getDataValue('avg_execution_time')).toFixed(2) : null,
// //       averageTimeSpent: averageMetrics ?
// //         parseFloat(averageMetrics.getDataValue('avg_time_spent')).toFixed(2) : null,
// //       averageAttempts: averageMetrics ?
// //         parseFloat(averageMetrics.getDataValue('avg_attempts')).toFixed(1) : null
// //     },
// //     grades: {
// //       distribution: gradeDistribution.map(grade => ({
// //         grade: grade.letter_grade,
// //         count: parseInt(grade.getDataValue('count')),
// //         averageScore: parseFloat(grade.getDataValue('average_score')).toFixed(1)
// //       })),
// //       averageScore: gradeDistribution.length > 0 ?
// //         (gradeDistribution.reduce((acc, curr) =>
// //           acc + parseFloat(curr.getDataValue('average_score')), 0) / gradeDistribution.length).toFixed(1) : null
// //     },
// //     metadata: {
// //       projectTitle: project.title,
// //       courseName: project.course.name,
// //       dueDate: project.due_date,
// //       generatedAt: new Date()
// //     }
// //   };

// //   // Cache statistics
// //   await cache.set(`project_stats_${projectId}`, statistics, 3600); // Cache for 1 hour

// //   // Log statistics generation
// //   await ActivityLog.create({
// //     user_id: req.user.id,
// //     activity_type: 'statistics_generated',
// //     resource_type: 'project',
// //     resource_id: projectId,
// //     metadata: {
// //       totalSubmissions,
// //       submissionRate: statistics.overview.submissionRate,
// //       timestamp: new Date()
// //     }
// //   });

// //   await buildSuccessResponse(
// //     req,
// //     res,
// //     { statistics },
// //     'Submission statistics retrieved successfully',
// //     component,
// //     auditComponent,
// //     httpStatus.OK
// //   );
// // });

// // /**
// //  * @desc    Auto-save submission (for periodic saves)
// //  * @route   POST /api/submissions/autosave
// //  * @access  Private (Student)
// //  */

// // export const autoSaveSubmission = asyncHandler(async (req, res) => {
// //   component = 'autoSaveSubmission';
// //   auditComponent = 'Auto Save Submission Draft';

// //   const {
// //     projectId,
// //     notebookContent,
// //     currentProgress = 0,
// //     executionOutput = null,
// //     metadata = {}
// //   } = req.body;

// //   if (!projectId || !notebookContent) {
// //     throw new Error('Project ID and notebook content are required');
// //   }

// //   // Find or create submission
// //   let submission = await Submission.findOne({
// //     where: {
// //       project_id: projectId,
// //       user_id: req.user.id
// //     },
// //     include: [{
// //       model: Project,
// //       as: 'project',
// //       attributes: ['id', 'title', 'due_date', 'status']
// //     }]
// //   });

// //   // Validate project status and deadline
// //   if (submission?.project) {
// //     if (submission.project.status !== 'published') {
// //       throw new Error('Cannot save submission for unpublished project');
// //     }

// //     if (submission.project.due_date && new Date() > new Date(submission.project.due_date)) {
// //       throw new Error('Cannot save submission after deadline');
// //     }
// //   }

// //   // Create new submission if doesn't exist
// //   if (!submission) {
// //     submission = await Submission.create({
// //       project_id: projectId,
// //       user_id: req.user.id,
// //       status: 'in_progress',
// //       attempts: 1,
// //       time_spent: 0,
// //       metadata
// //     });
// //   }

// //   // Validate submission status
// //   if (submission.status !== 'in_progress') {
// //     throw new Error('Cannot auto-save submitted assignment');
// //   }

// //   try {
// //     // Generate unique filename with timestamp
// //     const timestamp = Date.now();
// //     const fileName = `submissions/${req.user.id}/${projectId}/autosave_${timestamp}.ipynb`;

// //     // Upload notebook content to S3
// //     const s3Url = await S3Service.uploadFile(
// //       Buffer.from(notebookContent),
// //       fileName,
// //       'application/json'
// //     );

// //     // Delete old autosave file if it exists
// //     if (submission.notebook_s3_url) {
// //       try {
// //         await S3Service.deleteFile(submission.notebook_s3_url);
// //       } catch (error) {
// //         LoggerError(req, 'Error deleting old autosave', component, httpStatus.INTERNAL_SERVER_ERROR, error);
// //       }
// //     }

// //     // Calculate time spent
// //     const lastSaved = submission.metadata?.lastAutoSave ? 
// //       new Date(submission.metadata.lastAutoSave) : null;
// //     const timeSpent = lastSaved ? 
// //       submission.time_spent + (Date.now() - lastSaved.getTime()) / 1000 : 
// //       submission.time_spent;

// //     // Update submission with new data
// //     const updatedSubmission = await submission.update({
// //       notebook_s3_url: s3Url,
// //       time_spent: timeSpent,
// //       current_progress: currentProgress,
// //       execution_output: executionOutput,
// //       metadata: {
// //         ...submission.metadata,
// //         ...metadata,
// //         lastAutoSave: new Date(),
// //         autoSaveCount: (submission.metadata?.autoSaveCount || 0) + 1,
// //         lastModified: new Date()
// //       }
// //     });

// //     // Update student progress
// //     await StudentProjectProgress.upsert({
// //       student_id: req.user.id,
// //       project_id: projectId,
// //       last_activity: new Date(),
// //       current_progress: currentProgress
// //     });

// //     // Log auto-save activity
// //     await StudentActivity.create({
// //       student_id: req.user.id,
// //       project_id: projectId,
// //       activity_type: 'auto_save',
// //       metadata: {
// //         submissionId: submission.id,
// //         timestamp: new Date(),
// //         progress: currentProgress,
// //         timeSpent
// //       }
// //     });

// //     const responseData = {
// //       lastSaved: new Date(),
// //       submissionId: updatedSubmission.id,
// //       timeSpent: updatedSubmission.time_spent,
// //       currentProgress,
// //       autoSaveCount: updatedSubmission.metadata.autoSaveCount,
// //       notebookS3Url: updatedSubmission.notebook_s3_url
// //     };

// //     await buildSuccessResponse(
// //       req,
// //       res,
// //       responseData,
// //       'Auto-save successful',
// //       component,
// //       auditComponent,

// //       httpStatus.OK
// //     );

// //   } catch (error) {
// //     LoggerError(req, 'Auto-save error', component, httpStatus.INTERNAL_SERVER_ERROR, error);
// //     throw new Error('Failed to auto-save submission');
// //   }
// // });
