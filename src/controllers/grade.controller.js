import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import gradeService from '../services/grade.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

let component, auditComponent;

/**
 * @desc    Get all grades with pagination and filtering
 * @route   GET /api/grades
 * @access  Private
 */
export const getGrades = asyncHandler(
  async (req, res) => {
    component = 'getGrades';
    auditComponent = 'Get Grades';
    const result = await gradeService.getGrades(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grades retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get grade by ID
 * @route   GET /api/grades/:id
 * @access  Private
 */
export const getGradeById = asyncHandler(
  async (req, res) => {
    component = 'getGradeById';
    auditComponent = 'Get Grade By ID';
    const result = await gradeService.getGradeById(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grade retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Create or update grade
 * @route   POST /api/grades
 * @access  Private (Instructor, Admin)
 */
export const createOrUpdateGrade = asyncHandler(
  async (req, res) => {
    component = 'createOrUpdateGrade';
    auditComponent = 'Create Or Update Grade';
    const result = await gradeService.createOrUpdateGrade(req.body, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grade saved successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Update grade
 * @route   PUT /api/grades/:id
 * @access  Private (Instructor, Admin)
 */
export const updateGrade = asyncHandler(
  async (req, res) => {
    component = 'updateGrade';
    auditComponent = 'Update Grade';
    const result = await gradeService.updateGrade(req.params.id, req.body, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grade updated successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Delete grade
 * @route   DELETE /api/grades/:id
 * @access  Private (Instructor, Admin)
 */
export const deleteGrade = asyncHandler(
  async (req, res) => {
    component = 'deleteGrade';
    auditComponent = 'Delete Grade';
    const result = await gradeService.deleteGrade(req.params.id, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grade deleted successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get grading queue (submissions ready to grade)
 * @route   GET /api/grades/queue
 * @access  Private (Instructor, Admin)
 */
export const getGradingQueue = asyncHandler(
  async (req, res) => {
    component = 'getGradingQueue';
    auditComponent = 'Get Grading Queue';
    const result = await gradeService.getGradingQueue(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grading queue retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get grade statistics for a project
 * @route   GET /api/grades/project/:projectId/statistics
 * @access  Private (Instructor, Admin)
 */
export const getGradeStatistics = asyncHandler(
  async (req, res) => {
    component = 'getGradeStatistics';
    auditComponent = 'Get Grade Statistics';
    const result = await gradeService.getGradeStatistics(req.params.projectId, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grade statistics retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Bulk grade submissions
 * @route   POST /api/grades/bulk
 * @access  Private (Instructor, Admin)
 */
export const bulkGradeSubmissions = asyncHandler(
  async (req, res) => {
    component = 'bulkGradeSubmissions';
    auditComponent = 'Bulk Grade Submissions';
    const result = await gradeService.bulkGradeSubmissions(req.body, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Bulk grading completed successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Create grade using rubric validation
 * @route   POST /api/grades/rubric-based
 * @access  Private (Instructor, Admin)
 */
export const createRubricBasedGrade = asyncHandler(
  async (req, res) => {
    component = 'createRubricBasedGrade';
    auditComponent = 'Create Rubric Based Grade';
    const result = await gradeService.createRubricBasedGrade(req.body, req.user);
    const message = result.message || 'Grade created successfully using rubric validation';
    delete result.message;
    await buildSuccessResponse(
      req,
      res,
      result,
      message,
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get rubric data for submission grading
 * @route   GET /api/grades/submission/:submissionId/rubric-data
 * @access  Private (Instructor, Admin)
 */
export const getSubmissionRubricData = asyncHandler(
  async (req, res) => {
    component = 'getSubmissionRubricData';
    auditComponent = 'Get Submission Rubric Data';
    const result = await gradeService.getSubmissionRubricData(req.params.submissionId, req.user.id);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Submission rubric data retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);


/**
 * @desc    Get grading history for a submission
 * @route   GET /api/grades/submission/:submissionId/history
 * @access  Private (Instructor, Admin)
 */
export const getSubmissionGradingHistory = asyncHandler(
  async (req, res) => {
    component = 'getSubmissionGradingHistory';
    auditComponent = 'Get Submission Grading History';
    const result = await gradeService.getSubmissionGradingHistory(req.params.submissionId, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Grading history retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get submission progress details with checkpoint and rubric info
 * @route   GET /api/grades/submission/:submissionId/progress-details
 * @access  Private (Instructor, Admin)
 */
export const getSubmissionProgressDetails = asyncHandler(
  async (req, res) => {
    component = 'getSubmissionProgressDetails';
    auditComponent = 'Get Submission Progress Details';
    const result = await gradeService.getSubmissionProgressDetails(req.params.submissionId, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Submission progress details retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get comprehensive grade statistics with student details
 * @route   GET /api/grades/comprehensive-statistics
 * @access  Private (Instructor, Admin)
 */
export const getComprehensiveGradeStatistics = asyncHandler(
  async (req, res) => {
    component = 'getComprehensiveGradeStatistics';
    auditComponent = 'Get Comprehensive Grade Statistics';
    const result = await gradeService.getComprehensiveGradeStatistics(req.query, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Comprehensive grade statistics retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get detailed student information
 * @route   GET /api/grades/student/:studentId/details
 * @access  Private (Instructor, Admin)
 */
export const getStudentDetails = asyncHandler(
  async (req, res) => {
    component = 'getStudentDetails';
    auditComponent = 'Get Student Details';
    const result = await gradeService.getStudentDetails(req.params.studentId, req.user);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Student details retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get student grade summary
 * @route   GET /api/grades/student/summary
 * @access  Private (Student)
 */
export const getStudentGradeSummary = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;
    const { courseId } = req.query;

    // Get all grades for the student
    const grades = await Grade.findAll({
      include: [{
        model: Submission,
        as: 'submission',
        where: { user_id: studentId },
        include: [{
          model: Project,
          as: 'project',
          include: [{
            model: Course,
            as: 'course',
            ...(courseId && { where: { id: courseId } })
          }]
        }]
      }],
      order: [['graded_at', 'DESC']]
    });

    // Calculate summary statistics
    const totalGrades = grades.length;
    const totalPoints = grades.reduce((sum, grade) => sum + (grade.total_score || 0), 0);
    const maxPoints = grades.reduce((sum, grade) => sum + (grade.max_score || 0), 0);
    const averagePercentage = totalGrades > 0 ? (totalPoints / maxPoints) * 100 : 0;

    // Grade distribution
    const gradeDistribution = {
      A: grades.filter(g => g.percentage >= 90).length,
      B: grades.filter(g => g.percentage >= 80 && g.percentage < 90).length,
      C: grades.filter(g => g.percentage >= 70 && g.percentage < 80).length,
      D: grades.filter(g => g.percentage >= 60 && g.percentage < 70).length,
      F: grades.filter(g => g.percentage < 60).length
    };

    // Recent grades (last 5)
    const recentGrades = grades.slice(0, 5).map(grade => ({
      id: grade.id,
      projectTitle: grade.submission.project.title,
      courseName: grade.submission.project.course.name,
      score: grade.total_score,
      maxScore: grade.max_score,
      percentage: grade.percentage,
      letterGrade: grade.letter_grade,
      gradedAt: grade.graded_at
    }));

    res.json({
      success: true,
      data: {
        summary: {
          totalGrades,
          totalPoints,
          maxPoints,
          averagePercentage: Math.round(averagePercentage * 100) / 100,
          gradeDistribution
        },
        recentGrades
      }
    });
  } catch (error) {
    logger.error('Error getting student grade summary:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve grade summary'
    });
  }
});

/**
 * @desc    Get detailed grades for a specific project
 * @route   GET /api/grades/project/:projectId
 * @access  Private (Student)
 */
export const getProjectGradeDetails = asyncHandler(
  async (req, res) => {
    component = 'getProjectGradeDetails';
    auditComponent = 'Get project grade details';
    const result = await gradeService.getProjectGradeDetails(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Fetch project grade details successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get checkpoint grades for a project
 * @route   GET /api/grades/checkpoints/:projectId
 * @access  Private (Student)
 */
export const getCheckpointGrades = asyncHandler(
  async (req, res) => {
    component = 'getCheckpointGrades';
    auditComponent = 'Get project grade details';
    const result = await gradeService.getCheckpointGrades(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Fetch project checkpoint grade details successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);