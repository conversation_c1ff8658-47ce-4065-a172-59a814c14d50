import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import { User, Role, Permission } from '../models/associations.js';
import logger from './logger.config.js';

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL:
        process.env.GOOGLE_CALLBACK_URL || '/api/auth/google/callback'
    },
    async (_accessToken, _refreshToken, profile, done) => {
      try {
        const userEmail = profile.emails[0].value;
        logger.info(`Google OAuth login attempt for: ${userEmail}`);

        // Check if user exists by email (only allow existing users)
        const user = await User.findOne({
          where: { email: userEmail },
          include: [
            {
              model: Role,
              as: 'roles',
              include: [
                {
                  model: Permission,
                  as: 'permissions'
                }
              ]
            }
          ]
        });

        // If user doesn't exist, reject authentication
        if (!user) {
          logger.warn(
            `Google OAuth login rejected - user not found: ${userEmail}`
          );
          return done(null, false, {
            message: 'User not registered. Please contact your administrator.'
          });
        }

        // Check if user has required roles (admin or instructor)
        const hasRequiredRole = user.roles?.some(
          role => role.name === 'admin' || role.name === 'instructor'
        );

        if (!hasRequiredRole) {
          logger.warn(
            `Google OAuth login rejected - insufficient role for user: ${userEmail}`
          );
          return done(null, false, {
            message:
              'Access denied. Only admin and instructor roles are allowed.'
          });
        }

        // Check if user account is active
        if (user.status !== 'active') {
          logger.warn(
            `Google OAuth login rejected - inactive account: ${userEmail}`
          );
          return done(null, false, {
            message: 'Account is inactive. Please contact your administrator.'
          });
        }

        // Link Google account if not already linked
        if (!user.google_id) {
          await user.update({
            google_id: profile.id,
            profile_picture: profile.photos[0]?.value || user.profile_picture
            // name: profile.displayName || user.name
          });
          logger.info(`Google account linked to existing user: ${userEmail}`);
        } else {
          // Update last login and profile picture if changed
          await user.update({
            last_login: new Date(),
            profile_picture: profile.photos[0]?.value || user.profile_picture
            // name: profile.displayName || user.name
          });
        }

        logger.info(
          `Google OAuth login successful for: ${userEmail} with roles: ${user.roles?.map(r => r.name).join(', ')}`
        );
        return done(null, user);
      } catch (error) {
        logger.error('Google OAuth error:', error);
        return done(error, null);
      }
    }
  )
);

// JWT Strategy
passport.use(
  new JwtStrategy(
    {
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_SECRET,
      passReqToCallback: true
    },
    async (req, payload, done) => {
      try {
        const user = await User.findByPk(payload.userId, {
          include: [
            {
              model: Role,
              as: 'roles',
              include: [
                {
                  model: Permission,
                  as: 'permissions'
                }
              ]
            }
          ]
        });

        if (user && user.status === 'active') {
          // Add user permissions to req for RBAC middleware
          req.userPermissions = [];
          user.roles?.forEach(role => {
            role.permissions?.forEach(permission => {
              if (!req.userPermissions.includes(permission.key)) {
                req.userPermissions.push(permission.key);
              }
            });
          });

          return done(null, user);
        }

        return done(null, false);
      } catch (error) {
        logger.error('JWT verification error:', error);
        return done(error, false);
      }
    }
  )
);

// Serialize/Deserialize user for session management (if using sessions)
passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id, {
      include: [
        {
          model: Role,
          as: 'roles',
          include: [
            {
              model: Permission,
              as: 'permissions'
            }
          ]
        }
      ]
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

export default passport;
