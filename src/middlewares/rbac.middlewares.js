import logger from '../config/logger.config.js';
import {
  mapLtiRolesToInternal,
  getHighestPriorityRole,
  hasAnyPermission,
  canAccessRoute,
  getRoutePermission,
  INTERNAL_ROLES,
  PERMISSIONS
} from '../config/roleMapping.config.js';

/**
 * Enhanced RBAC (Role-Based Access Control) Middleware
 *
 * Provides comprehensive role and permission checking for LTI sessions
 * with configurable role mappings and permission enforcement.
 */

/**
 * Middleware to require specific internal roles
 * @param {Array<string>} requiredRoles - Array of required internal role names
 * @returns {Function} Express middleware function
 */
export const requireRole = requiredRoles => {
  return (req, res, next) => {
    try {
      // Check if session exists
      if (!req.session) {
        logger.warn('[RBAC] No session found for role check', {
          path: req.path,
          method: req.method,
          requiredRoles
        });

        return res.status(401).json({
          error: 'session_expired',
          reason: 'No active session found',
          relaunchHint: true,
          message: 'Please relaunch from your learning management system'
        });
      }

      // Get user's internal roles from session
      const userInternalRoles = req.session.roles || [];

      if (!Array.isArray(userInternalRoles) || userInternalRoles.length === 0) {
        logger.warn('[RBAC] No internal roles found in session', {
          path: req.path,
          method: req.method,
          requiredRoles,
          sessionId: req.sessionID
        });

        return res.status(403).json({
          error: 'insufficient_roles',
          reason: 'No internal roles assigned',
          relaunchHint: true,
          message: 'You do not have the required permissions for this resource'
        });
      }

      // Check if user has any of the required roles
      const hasRequiredRole = requiredRoles.some(role =>
        userInternalRoles.includes(role)
      );

      if (!hasRequiredRole) {
        logger.warn('[RBAC] Insufficient internal roles', {
          path: req.path,
          method: req.method,
          requiredRoles,
          userRoles: userInternalRoles,
          sessionId: req.sessionID
        });

        return res.status(403).json({
          error: 'insufficient_roles',
          reason: `Required roles: ${requiredRoles.join(', ')}`,
          relaunchHint: true,
          message: 'You do not have the required permissions for this resource'
        });
      }

      // Add role info to request for use in controllers
      req.userRoles = {
        internal: userInternalRoles,
        lti: req.session.lti_roles || [],
        highest: getHighestPriorityRole(userInternalRoles)
      };

      next();
    } catch (error) {
      logger.error('[RBAC] Error in requireRole middleware:', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        requiredRoles
      });

      return res.status(500).json({
        error: 'rbac_error',
        reason: 'Internal role checking error',
        relaunchHint: true,
        message: 'Please try relaunching from your learning management system'
      });
    }
  };
};
export const requireRoles = (requiredRoles, options = {}) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User must be authenticated to access this resource'
        });
      }

      const roles = Array.isArray(requiredRoles)
        ? requiredRoles
        : [requiredRoles];
      const userRoles = req.userRoles || [];

      // Check for super admin role (bypass role checks)
      if (userRoles.includes('super_admin') && !options.excludeSuperAdmin) {
        return next();
      }

      const requireAll = options.requireAll !== false;
      let hasAccess = false;

      if (requireAll) {
        hasAccess = roles.every(role => userRoles.includes(role));
      } else {
        hasAccess = roles.some(role => userRoles.includes(role));
      }

      if (!hasAccess) {
        logger.warn(
          `Role access denied for user ${req.user.email}. Required: ${roles.join(', ')}, Has: ${userRoles.join(', ')}`
        );

        return res.status(403).json({
          error: 'Insufficient role privileges',
          message: 'You do not have the required role to access this resource',
          required: roles,
          userRoles: userRoles
        });
      }

      next();
    } catch (error) {
      logger.error('Role middleware error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Internal server error during role authorization'
      });
    }
  };
};

export const requirePermissions = (requiredPermissions, options = {}) => {
  return (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User must be authenticated to access this resource'
        });
      }

      // Convert single permission to array
      const permissions = Array.isArray(requiredPermissions)
        ? requiredPermissions
        : [requiredPermissions];

      // Get user permissions from request (set by auth middleware)
      const userPermissions = req.userPermissions || [];
      const userRoles = req.userRoles || [];

      // Check for super admin role (bypass permission checks)
      if (userRoles.includes('super_admin') && !options.excludeSuperAdmin) {
        logger.debug(
          `Super admin ${req.user.email} bypassing permission check`
        );
        return next();
      }

      // Determine if we need ALL permissions or ANY permission
      const requireAll = options.requireAll !== false; // Default to true

      let hasAccess = false;

      if (requireAll) {
        // User must have ALL required permissions
        hasAccess = permissions.every(permission =>
          userPermissions.includes(permission)
        );
      } else {
        // User needs ANY of the required permissions
        hasAccess = permissions.some(permission =>
          userPermissions.includes(permission)
        );
      }

      if (!hasAccess) {
        logger.warn(
          `Access denied for user ${req.user.email}. Required: ${permissions.join(', ')}, Has: ${userPermissions.join(', ')}`
        );

        return res.status(403).json({
          error: 'Insufficient permissions',
          message:
            'You do not have the required permissions to access this resource',
          required: permissions,
          userPermissions: userPermissions
        });
      }

      // Log successful permission check
      logger.debug(
        `Permission granted for user ${req.user.email}. Required: ${permissions.join(', ')}`
      );

      next();
    } catch (error) {
      logger.error('RBAC middleware error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Internal server error during authorization'
      });
    }
  };
};

/**
 * Middleware to require specific permissions
 * @param {Array<string>} requiredPermissions - Array of required permission keys
 * @returns {Function} Express middleware function
 */
export const requirePermission = requiredPermissions => {
  return (req, res, next) => {
    try {
      // Check if session exists
      if (!req.session) {
        logger.warn('[RBAC] No session found for permission check', {
          path: req.path,
          method: req.method,
          requiredPermissions
        });

        return res.status(401).json({
          error: 'session_expired',
          reason: 'No active session found',
          relaunchHint: true,
          message: 'Please relaunch from your learning management system'
        });
      }

      // Get user's internal roles from session
      const userInternalRoles = req.session.roles || [];

      if (!Array.isArray(userInternalRoles) || userInternalRoles.length === 0) {
        logger.warn('[RBAC] No internal roles found for permission check', {
          path: req.path,
          method: req.method,
          requiredPermissions,
          sessionId: req.sessionID
        });

        return res.status(403).json({
          error: 'insufficient_permissions',
          reason: 'No internal roles assigned',
          relaunchHint: true,
          message: 'You do not have the required permissions for this resource'
        });
      }

      // Check if user has any of the required permissions
      const hasRequiredPermission = hasAnyPermission(
        userInternalRoles,
        requiredPermissions
      );

      if (!hasRequiredPermission) {
        logger.warn('[RBAC] Insufficient permissions', {
          path: req.path,
          method: req.method,
          requiredPermissions,
          userRoles: userInternalRoles,
          sessionId: req.sessionID
        });

        return res.status(403).json({
          error: 'insufficient_permissions',
          reason: `Required permissions: ${requiredPermissions.join(', ')}`,
          relaunchHint: true,
          message: 'You do not have the required permissions for this resource'
        });
      }

      // Add permission info to request
      req.userPermissions = {
        roles: userInternalRoles,
        permissions: requiredPermissions
      };

      next();
    } catch (error) {
      logger.error('[RBAC] Error in requirePermission middleware:', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        requiredPermissions
      });

      return res.status(500).json({
        error: 'rbac_error',
        reason: 'Internal permission checking error',
        relaunchHint: true,
        message: 'Please try relaunching from your learning management system'
      });
    }
  };
};

/**
 * Middleware to automatically check route permissions
 * This middleware automatically determines required permissions based on route
 */
export const requireRoutePermission = (req, res, next) => {
  try {
    // Check if session exists
    if (!req.session) {
      logger.warn('[RBAC] No session found for route permission check', {
        path: req.path,
        method: req.method
      });

      return res.status(401).json({
        error: 'session_expired',
        reason: 'No active session found',
        relaunchHint: true,
        message: 'Please relaunch from your learning management system'
      });
    }

    // Get user's internal roles from session
    const userInternalRoles = req.session.roles || [];

    if (!Array.isArray(userInternalRoles) || userInternalRoles.length === 0) {
      logger.warn('[RBAC] No internal roles found for route permission check', {
        path: req.path,
        method: req.method,
        sessionId: req.sessionID
      });

      return res.status(403).json({
        error: 'insufficient_permissions',
        reason: 'No internal roles assigned',
        relaunchHint: true,
        message: 'You do not have the required permissions for this resource'
      });
    }

    // Check if user can access this route
    const canAccess = canAccessRoute(userInternalRoles, req.method, req.path);

    if (!canAccess) {
      const requiredPermission = getRoutePermission(req.method, req.path);

      logger.warn('[RBAC] Route access denied', {
        path: req.path,
        method: req.method,
        requiredPermission,
        userRoles: userInternalRoles,
        sessionId: req.sessionID
      });

      return res.status(403).json({
        error: 'insufficient_permissions',
        reason: requiredPermission
          ? `Required permission: ${requiredPermission}`
          : 'Access denied for this route',
        relaunchHint: true,
        message: 'You do not have the required permissions for this resource'
      });
    }

    // Add route permission info to request
    req.routePermission = {
      method: req.method,
      path: req.path,
      requiredPermission: getRoutePermission(req.method, req.path),
      userRoles: userInternalRoles
    };

    next();
  } catch (error) {
    logger.error('[RBAC] Error in requireRoutePermission middleware:', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method
    });

    return res.status(500).json({
      error: 'rbac_error',
      reason: 'Internal route permission checking error',
      relaunchHint: true,
      message: 'Please try relaunching from your learning management system'
    });
  }
};

/**
 * Middleware to check if user has higher or equal role hierarchy
 * @param {string} minimumRole - Minimum required role in hierarchy
 * @returns {Function} Express middleware function
 */
export const requireMinimumRole = minimumRole => {
  return (req, res, next) => {
    try {
      // Check if session exists
      if (!req.session) {
        logger.warn('[RBAC] No session found for minimum role check', {
          path: req.path,
          method: req.method,
          minimumRole
        });

        return res.status(401).json({
          error: 'session_expired',
          reason: 'No active session found',
          relaunchHint: true,
          message: 'Please relaunch from your learning management system'
        });
      }

      // Get user's internal roles from session
      const userInternalRoles = req.session.roles || [];

      if (!Array.isArray(userInternalRoles) || userInternalRoles.length === 0) {
        logger.warn('[RBAC] No internal roles found for minimum role check', {
          path: req.path,
          method: req.method,
          minimumRole,
          sessionId: req.sessionID
        });

        return res.status(403).json({
          error: 'insufficient_roles',
          reason: 'No internal roles assigned',
          relaunchHint: true,
          message: 'You do not have the required permissions for this resource'
        });
      }

      // Get user's highest role
      const userHighestRole = getHighestPriorityRole(userInternalRoles);

      // Check if user's highest role meets minimum requirement
      // This would need to be implemented based on your role hierarchy
      const hasMinimumRole =
        userInternalRoles.includes(minimumRole) ||
        userInternalRoles.some(role => {
          // Add logic to check if role is higher than minimum
          // This is a simplified check - you might want to implement
          // a more sophisticated hierarchy comparison
          return (
            role === INTERNAL_ROLES.ADMINISTRATOR ||
            role === INTERNAL_ROLES.SYSTEM_ADMIN
          );
        });

      if (!hasMinimumRole) {
        logger.warn('[RBAC] Insufficient role hierarchy', {
          path: req.path,
          method: req.method,
          minimumRole,
          userHighestRole,
          userRoles: userInternalRoles,
          sessionId: req.sessionID
        });

        return res.status(403).json({
          error: 'insufficient_roles',
          reason: `Minimum role required: ${minimumRole}`,
          relaunchHint: true,
          message: 'You do not have the required permissions for this resource'
        });
      }

      // Add role hierarchy info to request
      req.roleHierarchy = {
        userHighestRole,
        minimumRole,
        userRoles: userInternalRoles
      };

      next();
    } catch (error) {
      logger.error('[RBAC] Error in requireMinimumRole middleware:', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        minimumRole
      });

      return res.status(500).json({
        error: 'rbac_error',
        reason: 'Internal role hierarchy checking error',
        relaunchHint: true,
        message: 'Please try relaunching from your learning management system'
      });
    }
  };
};

/**
 * Middleware to add user role and permission info to request
 * This middleware doesn't block access but adds role info for use in controllers
 */
export const addUserRoleInfo = (req, res, next) => {
  try {
    if (req.session && req.session.roles) {
      req.userRoleInfo = {
        internal: req.session.roles || [],
        lti: req.session.lti_roles || [],
        highest: getHighestPriorityRole(req.session.roles || []),
        permissions: req.session.roles
          ? req.session.roles.reduce((perms, role) => {
              // This would need to be implemented to get actual permissions
              // For now, just add the role as a permission placeholder
              return [...perms, role];
            }, [])
          : []
      };
    }

    next();
  } catch (error) {
    logger.error('[RBAC] Error in addUserRoleInfo middleware:', {
      error: error.message,
      stack: error.stack,
      path: req.path
    });

    // Don't block the request, just log the error
    next();
  }
};

/**
 * A middleware that enforces permissions only if a specific key exists in the request body.
 *
 * @param {string} bodyKey - The key to look for in req.body (e.g., 'projectId').
 * @param {string|string[]} requiredPermissions - The permission(s) to require if the key exists.
 */
export const requirePermissionIfKeyExists = (bodyKey, requiredPermissions) => {
  return (req, res, next) => {
    try {
      // Check if the dynamic key (e.g., 'projectId') exists in the body
      if (req.body[bodyKey]) {
        // If it exists, run the actual permission check with the REQUIRED permissions.
        // The requirePermissions function is already robust, so we just use it.
        return requirePermissions(requiredPermissions)(req, res, next);
      }

      // If the key does not exist, do nothing and proceed.
      return next();
    } catch (err) {
      // Always pass errors to the Express error handler
      return next(err);
    }
  };
};

/**
 * Convenience middleware for common role combinations
 */
export const requireProjectOwner = requireRole([INTERNAL_ROLES.PROJECT_OWNER]);
export const requireGrader = requireRole([
  INTERNAL_ROLES.GRADER,
  INTERNAL_ROLES.PROJECT_OWNER
]);
export const requireStudent = requireRole([INTERNAL_ROLES.STUDENT]);
export const requireAdmin = requireRole([
  INTERNAL_ROLES.ADMINISTRATOR,
  INTERNAL_ROLES.SYSTEM_ADMIN
]);

/**
 * Convenience middleware for common permission combinations
 */
export const requireProjectCreate = requirePermission([
  PERMISSIONS.PROJECT_CREATE
]);
export const requireProjectGrade = requirePermission([
  PERMISSIONS.PROJECT_GRADE
]);
export const requireUserManage = requirePermission([
  PERMISSIONS.USER_MANAGE_ROLES
]);
export const requireSystemConfig = requirePermission([
  PERMISSIONS.SYSTEM_CONFIG
]);

export default {
  requireRole,
  requirePermission,
  requireRoutePermission,
  requireMinimumRole,
  addUserRoleInfo,
  requireProjectOwner,
  requireGrader,
  requireStudent,
  requireAdmin,
  requireProjectCreate,
  requireProjectGrade,
  requireUserManage,
  requireSystemConfig
};
