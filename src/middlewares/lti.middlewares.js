import Project from '../models/Project.models.js';
import LtiContext from '../models/LtiContext.models.js';
import LtiResourceLink from '../models/LtiResourceLink.models.js';
import LtiContextEnrollment from '../models/LtiContextEnrollment.models.js';
import Role from '../models/Role.models.js';
import Permission from '../models/Permission.models.js';
import { asyncHandler } from './errorHandler.middlewares.js';
import logger from '../config/logger.config.js';

const addCoursePermission = asyncHandler(async (req, res, next) => {
  // Check both params and body for identifiers
  const projectId = req.params.projectId || req.body.projectId;
  const courseId = req.params.courseId || req.body.courseId;
  const resourceLinkId = req.params.resourceLinkId || req.body.resourceLinkId;
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({
      error: 'Authentication Required',
      message: 'User must be authenticated to access LTI context'
    });
  }

  let contextId = null;
  let context = null;

  // Rule 1: When courseId is present (highest priority)
  if (courseId) {
    contextId = courseId;
  }
  // Rule 2: When resourceLinkId is present
  else if (resourceLinkId) {
    const resourceLink = await LtiResourceLink.findOne({
      where: { id: resourceLinkId }
    });

    if (!resourceLink) {
      return res.status(404).json({
        error: 'Resource Link Not Found',
        message: `Resource link with ID ${resourceLinkId} not found`
      });
    }

    contextId = resourceLink.contextId;
  }
  // Rule 3: When projectId is present
  else if (projectId) {
    const project = await Project.findOne({
      where: { id: projectId },
      attributes: ['course_id']
    });

    if (!project) {
      return res.status(404).json({
        error: 'Project Not Found',
        message: `Project with ID ${projectId} not found`
      });
    }

    if (!project.course_id) {
      return res.status(400).json({
        error: 'Project Not Linked',
        message: `Project ${projectId} is not linked to any course`
      });
    }

    contextId = project.course_id;
  } else {
    return res.status(400).json({
      error: 'Missing Identifier',
      message: 'One of projectId, courseId, or resourceLinkId is required'
    });
  }

  if (!contextId) {
    return res.status(400).json({
      error: 'Missing Context ID',
      message: `Context ID could not be determined`
    });
  }

  // Fetch context details from lti_contexts
  context = await LtiContext.findOne({
    where: { id: contextId, isActive: true }
  });

  if (!context) {
    return res.status(404).json({
      error: 'Context Not Found',
      message: `LTI context with ID ${contextId} not found or inactive`
    });
  }

  // Fetch user's role for this context
  const enrollment = await LtiContextEnrollment.findOne({
    where: {
      user_id: userId,
      context_id: contextId,
      enrollment_status: 'active'
    }
  });

  if (!enrollment) {
    return res.status(403).json({
      error: 'Not Enrolled',
      message: `User is not enrolled in context ${contextId}`
    });
  }

  // Attach context data to request
  req.context = {
    id: context.id,
    platformId: context.platformId,
    deploymentId: context.deploymentId,
    contextId: context.contextId,
    contextType: context.contextType,
    contextTitle: context.contextTitle,
    contextLabel: context.contextLabel,
    agsClaimEndpoint: context.agsClaimEndpoint,
    nrpsClaimEndpoint: context.nrpsClaimEndpoint,
    isActive: context.isActive
  };

  // Attach user's role
  req.courseRole = enrollment.role_in_course;

  // Attach derived permissions based on role
  req.courseRolePermission = await getRolePermissions(
    enrollment.role_in_course
  );

  next();
});

async function getRolePermissions(role) {
  try {
    // Find the role by name
    const roleRecord = await Role.findOne({
      where: { name: role },
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    });

    if (!roleRecord || !roleRecord.permissions) {
      logger.warn(`Role not found or has no permissions: ${role}`);
      return {};
    }

    // Convert permissions array to permission object
    const permissions = roleRecord.permissions.map(
      permission => permission.key
    );
    // roleRecord.permissions.forEach(permission => {
    //   // Convert permission key like 'can_view_projects' to camelCase 'canViewProjects'
    //   const camelCaseKey = permission.key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    //   permissions[camelCaseKey] = true;
    // });

    return permissions;
  } catch (error) {
    logger.error('Error fetching role permissions:', error);
    return {};
  }
}

export default addCoursePermission;
