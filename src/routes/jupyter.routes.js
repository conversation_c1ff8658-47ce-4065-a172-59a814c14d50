import express from 'express';
import { body } from 'express-validator';
import multer from 'multer';
import {
  createWorkspace,
  executeCode,
  executeNotebook,
  uploadFile
} from '../controllers/jupyter.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import {
  attachJupyterToken,
  jupyterProxy
} from '../middlewares/jupyterhub.middlewares.js';
import { validate } from '../middlewares/validation.middlewares.js';


// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (_req, _file, cb) => {
    // Allow all file types for Jupyter workspace uploads
    cb(null, true);
  }
});

const router = express.Router();

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/execute:
 *   post:
 *     summary: Execute code in a kernel
 *     tags: [<PERSON><PERSON><PERSON>]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 example: "print('Hello, World!')"
 *     responses:
 *       200:
 *         description: Code execution result
 */
router.post(
  '/kernels/:kernelId/execute',
  jwtMiddleware,
  attachJupyterToken,
  executeCode
);

//Instructor sandbox boot up for submission files
// router.post(
//   '/submissions/:submissionId/sandbox',
//   jwtMiddleware,
//   // requirePermissions(['grade_submissions']), // This ensures only instructors can access it
//   attachJupyterToken,
//   createSubmissionSandbox
// );

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/execute-notebook:
 *   post:
 *     summary: Execute all cells in a notebook
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notebook:
 *                 type: object
 *                 description: Full Jupyter notebook JSON
 *               timeout:
 *                 type: integer
 *                 default: 30000
 *               stopOnError:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Notebook execution results
 */
router.post(
  '/kernels/:kernelId/execute-notebook',
  jwtMiddleware,
  attachJupyterToken,
  executeNotebook
);

// Create project workspace: starts server, creates /:projectId and Untitled.ipynb, starts session
router.post(
  '/:projectId/createWorkspace',
  jwtMiddleware,
  attachJupyterToken,
  createWorkspace
);

/**
 * @swagger
 * /api/jupyter/upload-file:
 *   post:
 *     summary: Upload file to user's project workspace in S3
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: File to upload
 *               projectId:
 *                 type: string
 *                 format: uuid
 *                 description: Project ID where file should be uploaded
 *             required:
 *               - file
 *               - projectId
 *     responses:
 *       201:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isSuccess:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                     key:
 *                       type: string
 *                     bucket:
 *                       type: string
 *                     size:
 *                       type: integer
 *                     contentType:
 *                       type: string
 *                     downloadUrl:
 *                       type: string
 *       400:
 *         description: Bad request - missing file or projectId
 *       401:
 *         description: Unauthorized
 *       413:
 *         description: File too large
 */
router.post(
  '/upload-file',
  jwtMiddleware,
  upload.single('file'),
  [body('projectId').isUUID().withMessage('Project ID must be a valid UUID')],
  validate,
  uploadFile
);

// Catch-all proxy (must be last)
router.use('/', jwtMiddleware, attachJupyterToken, jupyterProxy);

export default router;
