import express from 'express';
import {
  registerPlatform,
  getPlatforms,
  updatePlatform,
  deletePlatform,
  sendGradeToLMS,
  getCourseContexts,
  getTokenCacheStats,
  invalidatePlatformTokens,
  cleanupExpiredTokens,
  getLineItems,
  getLineItemByResourceLinkId,
  getStudents,
  getInstructors,
  createLineItem,
  postScore,
  listResults,
  getMyResult,
  getLTIServiceStatus,
  linkProjectToLmsLineItem,
  getUnassignedResourceLinks,
  getAllContexts,
  getContextMembers
} from '../controllers/lti.controller.js';
import { requireRoles } from '../middlewares/rbac.middlewares.js';
import { jwtAuthMiddleware } from '../middlewares/jwtAuth.middlewares.js';
import { body, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';
import addCoursePermission from '../middlewares/lti.middlewares.js';

const router = express.Router();
router.use('/', jwtAuthMiddleware);

/**
 * @swagger
 * /api/lti/platforms:
 *   get:
 *     summary: Get all registered LTI platforms
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Platforms retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/platforms', requireRoles(['admin']), getPlatforms);

/**
 * @swagger
 * /api/lti/platforms:
 *   post:
 *     summary: Register new LTI platform
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platformId
 *               - platformName
 *               - clientId
 *               - authLoginUrl
 *               - authTokenUrl
 *               - keySetUrl
 *             properties:
 *               platformId:
 *                 type: string
 *                 description: Platform issuer identifier
 *               platformName:
 *                 type: string
 *                 description: Human-readable platform name
 *               clientId:
 *                 type: string
 *                 description: OAuth client ID
 *               authLoginUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform authentication URL
 *               authTokenUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform token endpoint
 *               keySetUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform JWKS URL
 *               settings:
 *                 type: object
 *                 description: Additional platform settings
 *     responses:
 *       201:
 *         description: Platform registered successfully
 *       409:
 *         description: Platform already exists
 *       403:
 *         description: Insufficient permissions
 */
router.post(
  '/platforms',
  [
    requireRoles(['admin']),
    body('platformId').isURL(),
    body('platformName').isLength({ min: 2, max: 100 }),
    body('clientId').isString(),
    body('authLoginUrl').isURL(),
    body('authTokenUrl').isURL(),
    body('keySetUrl').isURL(),
    body('settings').optional().isObject()
  ],
  validate,
  registerPlatform
);

/**
 * @swagger
 * /api/lti/platforms/{id}:
 *   put:
 *     summary: Update LTI platform configuration
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               platformName:
 *                 type: string
 *               authLoginUrl:
 *                 type: string
 *                 format: uri
 *               authTokenUrl:
 *                 type: string
 *                 format: uri
 *               keySetUrl:
 *                 type: string
 *                 format: uri
 *               isActive:
 *                 type: boolean
 *               settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Platform updated successfully
 *       404:
 *         description: Platform not found
 *       403:
 *         description: Insufficient permissions
 */
router.put(
  '/platforms/:id',
  [
    requireRoles(['admin']),
    param('id').isUUID(),
    body('platformName').optional().isLength({ min: 2, max: 100 }),
    body('authLoginUrl').optional().isURL(),
    body('authTokenUrl').optional().isURL(),
    body('keySetUrl').optional().isURL(),
    body('isActive').optional().isBoolean(),
    body('settings').optional().isObject()
  ],
  validate,
  updatePlatform
);

/**
 * @swagger
 * /api/lti/platforms/{id}:
 *   delete:
 *     summary: Delete LTI platform
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Platform deleted successfully
 *       404:
 *         description: Platform not found
 *       409:
 *         description: Platform has active contexts
 *       403:
 *         description: Insufficient permissions
 */
router.delete(
  '/platforms/:id',
  [requireRoles(['admin']), param('id').isUUID()],
  validate,
  deletePlatform
);

/**
 * @swagger
 * /api/lti/grades:
 *   post:
 *     summary: Send grade to LMS via AGS
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     description: Sends a grade back to the LMS using Assignment and Grade Services
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - submissionId
 *               - gradeId
 *             properties:
 *               submissionId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the submission
 *               gradeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the grade to send
 *     responses:
 *       200:
 *         description: Grade sent to LMS successfully
 *       404:
 *         description: Submission or grade not found
 *       400:
 *         description: Project not linked to LTI resource
 *       500:
 *         description: Failed to send grade to LMS
 */
router.post(
  '/grades',
  [body('submissionId').isUUID(), body('gradeId').isUUID()],
  requireRoles(['admin', 'instructor'], { requireAll: false }),
  validate,
  sendGradeToLMS
);

/**
 * @swagger
 * /api/lti/contexts/course/{courseId}:
 *   get:
 *     summary: Get LTI contexts for a course
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Contexts retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/contexts/course/:courseId',
  [param('courseId').isUUID()],
  requireRoles(['admin', 'instructor'], { requireAll: false }),
  validate,
  getCourseContexts
);

/**
 * @swagger
 * /api/lti/cache/stats:
 *   get:
 *     summary: Get LTI token cache statistics
 *     tags: [LTI]
 *     description: Retrieve statistics about cached LTI service tokens
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 stats:
 *                   type: object
 *                   properties:
 *                     totalTokens:
 *                       type: integer
 *                     expiredTokens:
 *                       type: integer
 *                     validTokens:
 *                       type: integer
 *                     cacheKeys:
 *                       type: integer
 *       500:
 *         description: Failed to retrieve cache statistics
 */
router.get('/cache/stats', requireRoles(['admin']), getTokenCacheStats);

/**
 * @swagger
 * /api/lti/cache/platform/{platformId}:
 *   delete:
 *     summary: Invalidate cached tokens for a platform
 *     tags: [LTI]
 *     description: Remove cached tokens for a specific platform
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: platformId
 *         required: true
 *         schema:
 *           type: string
 *         description: Platform identifier
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               scopes:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Optional specific scopes to invalidate
 *     responses:
 *       200:
 *         description: Tokens invalidated successfully
 *       500:
 *         description: Failed to invalidate tokens
 */
router.delete(
  '/cache/platform/:platformId',
  requireRoles(['admin']),
  invalidatePlatformTokens
);

/**
 * @swagger
 * /api/lti/cache/cleanup:
 *   post:
 *     summary: Clean up expired tokens
 *     tags: [LTI]
 *     description: Remove all expired tokens from cache
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 cleanedCount:
 *                   type: integer
 *       500:
 *         description: Failed to cleanup expired tokens
 */
router.post('/cache/cleanup', requireRoles(['admin']), cleanupExpiredTokens);

/**
 * @swagger
 * /api/lti/ags/{resourceLinkId}/lineitems:
 *   get:
 *     summary: Fetch gradebook columns via AGS for a specific resource link
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     description: Uses AGS service URL from the last LTI launch to retrieve line items (gradebook columns) for a specific resource link
 *     parameters:
 *       - in: path
 *         name: resourceLinkId
 *         required: true
 *         schema:
 *           type: string
 *         description: Resource Link ID to fetch line items for
 *       - in: query
 *         name: tag
 *         schema:
 *           type: string
 *         description: Optional tag filter
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of line items to return
 *     responses:
 *       200:
 *         description: Line items (gradebook columns) returned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 lineItems:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       label:
 *                         type: string
 *                       scoreMaximum:
 *                         type: number
 *                       resourceLinkId:
 *                         type: string
 *                       tag:
 *                         type: string
 *                 totalItems:
 *                   type: integer
 *                 context:
 *                   type: object
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Missing launch context or AGS not available
 *       401:
 *         description: Unauthorized access token
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/ags/:resourceLinkId/lineitems',
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  getLineItems
);

/**
 * @swagger
 * /api/lti/ags/lineitems/:resourceLinkId:
 *   get:
 *     summary: Fetch a single gradebook column by resourceLinkId
 *     tags: [LTI]
 *     description: Returns the first line item that matches the provided resourceLinkId
 *     parameters:
 *       - in: path
 *         name: resourceLinkId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Resource Link ID to filter line items
 *     responses:
 *       200:
 *         description: Matching line item (if any)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 found:
 *                   type: boolean
 *                 lineItem:
 *                   type: object
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Missing required query parameter or launch context
 *       404:
 *         description: AGS endpoint not found or no matching item
 */
router.get(
  '/ags/lineitems/:resourceLinkId',
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  getLineItemByResourceLinkId
);

/**
 * @swagger
 * /api/lti/nrps/students:
 *   get:
 *     summary: Get student memberships only
 *     tags: [LTI]
 *     responses:
 *       200: { description: Students fetched }
 */
router.get(
  '/nrps/students',
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  getStudents
);

/**
 * @swagger
 * /api/lti/nrps/instructors:
 *   get:
 *     summary: Get instructor memberships only
 *     tags: [LTI]
 *     responses:
 *       200: { description: Instructors fetched }
 */
router.get(
  '/nrps/instructors',
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  getInstructors
);

/**
 * @swagger
 * /api/lti/ags/lineitems:
 *   post:
 *     summary: Create (and store) a new line item
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - label
 *               - scoreMaximum
 *             properties:
 *               label:
 *                 type: string
 *                 description: Label for the line item
 *               scoreMaximum:
 *                 type: number
 *                 description: Maximum possible score
 *               tag:
 *                 type: string
 *                 description: Optional tag for the line item
 *               resourceId:
 *                 type: string
 *                 description: Optional resource ID
 *     responses:
 *       201:
 *         description: Line item created successfully
 *       400:
 *         description: Invalid request data
 *       403:
 *         description: Insufficient permissions
 */
router.post(
  '/ags/lineitems',
  requireRoles(['instructor', 'admin'], { requireAll: false }),
  createLineItem
);

/**
 * @swagger
 * /api/lti/ags/{projectId}/scores:
 *   post:
 *     summary: Post a score to a line item (Grade Passback)
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID to post score for
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - score
 *               - userId
 *             properties:
 *               score:
 *                 type: number
 *                 description: Score to post
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: User ID to post score for
 *               activityProgress:
 *                 type: string
 *                 enum: [Initialized, Started, InProgress, Submitted, Completed]
 *                 description: Activity progress status
 *               gradingProgress:
 *                 type: string
 *                 enum: [NotReady, Ready, Failed, Pending, FullyGraded]
 *                 description: Grading progress status
 *     responses:
 *       200:
 *         description: Score posted successfully
 *       400:
 *         description: Invalid request data
 *       403:
 *         description: Insufficient permissions
 */
router.post(
  '/ags/:projectId/scores',
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  postScore
);

/**
 * @swagger
 * /api/lti/ags/{projectId}/results:
 *   get:
 *     summary: List all results for a line item
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID to get results for
 *     responses:
 *       200:
 *         description: Results retrieved successfully
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Project not found
 */
router.get(
  '/ags/:projectId/results',
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  listResults
);

/**
 * @swagger
 * /api/lti/ags/results/{projectId}/me:
 *   get:
 *     summary: Get current user result for a line item
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID to get result for
 *     responses:
 *       200:
 *         description: Result retrieved successfully
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Project not found
 */
router.get(
  '/ags/results/:projectId/me',
  [addCoursePermission, requireRoles(['student'], { requireAll: false })],
  getMyResult
);

/**
 * @swagger
 * /api/lti/status:
 *   get:
 *     summary: Get current LTI service/session status
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: LTI service status retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/status',
  requireRoles(['instructor', 'admin'], { requireAll: false }),
  getLTIServiceStatus
);

/**
 * @swagger
 * /api/lti/ags/link-project:
 *   post:
 *     summary: Link local project to LMS line item and update LMS resourceId
 *     tags: [LTI]
 *     description: Given resourceLinkId and projectId, updates local lti_resource_links and lti_line_items with projectId, updates project.course_id/total_points from line item, and performs a PUT to LMS lineitem to set resourceId.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [resourceLinkId, projectId]
 *             properties:
 *               resourceLinkId:
 *                 type: string
 *               projectId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Mapping updated and LMS line item resourceId set
 *       400:
 *         description: Validation or missing context
 *       404:
 *         description: Not found
 */
router.post(
  '/ags/link-project',
  [
    body('resourceLinkId').isString(),
    body('projectId').isUUID(),
    body('instructor_ids').optional().isArray(),
    body('instructor_ids.*').optional().isUUID(),
    body('teaching_ass_id').optional().isArray(),
    body('teaching_ass_id.*').optional().isUUID()
  ],
  validate,
  [
    addCoursePermission,
    requireRoles(['instructor', 'admin'], { requireAll: false })
  ],
  linkProjectToLmsLineItem
);

/**
 * @swagger
 * /api/lti/resource-links/unassigned:
 *   get:
 *     summary: Get unassigned LTI resource links
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: contextId
 *         schema:
 *           type: string
 *         description: Context ID filter
 *       - in: query
 *         name: platformId
 *         schema:
 *           type: string
 *         description: Platform ID filter
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: 'createdAt:desc'
 *         description: Sort order (e.g., 'createdAt:asc')
 *     responses:
 *       200:
 *         description: A paginated list of unassigned resource links
 *       403:
 *         description: Insufficient permissions
 */

router.get(
  '/resource-links/unassigned',
  requireRoles(['instructor', 'admin'], { requireAll: false }),
  getUnassignedResourceLinks
);

/**
 * @swagger
 * /api/lti/contexts:
 *   get:
 *     summary: Get all LTI contexts
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination (optional)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page (optional)
 *     responses:
 *       200:
 *         description: Contexts retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/contexts',
  requireRoles(['instructor', 'admin'], { requireAll: false }),
  getAllContexts
);

/**
 * @swagger
 * /api/lti/contexts/instructors:
 *   get:
 *     summary: Get context users (instructors/others) by context and platform
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: contextId
 *         required: true
 *         schema:
 *           type: string
 *         description: LtiContext primary key ID
 *       - in: query
 *         name: platformId
 *         required: true
 *         schema:
 *           type: string
 *         description: LtiPlatform primary key ID
 *       - in: query
 *         name: role
 *         required: false
 *         schema:
 *           type: string
 *           enum: [admin, instructor, ta, student]
 *         description: Optional role filter; 'admin' maps to 'instructor'. When omitted, returns all roles.
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *       400:
 *         description: Missing/invalid parameters
 */
router.get(
  '/contexts/members',
  requireRoles(['instructor', 'admin'], { requireAll: false }),
  getContextMembers
);

export default router;
