import express from 'express';
import {
  createEnhancedProject,
  createProjectTemplate,
  getProjectTemplates,
  getTemplateById,
  updateProjectTemplate,
  deleteProjectTemplate,
  featureProjectTemplate,
  assignUsersToProject,
  getProjectAssignments,
  removeUserAssignment,
  publishProject,
  unpublishProject,
  saveProjectAsDraft,
  getProjectWithDetails,
  duplicateProjectFromTemplate,
  rateProjectTemplate,
  getProjectsByUserAssignment,
  getUserProjectWorkload,
  getProjectAssignmentStats,
  getEnhancedProject,
  updateEnhancedProject,
  duplicateProject,
  deleteProject,
  createTemplateFromProject
} from '../controllers/enhancedProject.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import {
  requirePermissionIfKeyExists,
  requirePermissions
} from '../middlewares/rbac.middlewares.js';
import { body, query, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';
import addCoursePermission from '../middlewares/lti.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

// Create/update enhanced project
router.post(
  '/enhanced',
  [
    requirePermissions(['create_projects', 'edit_projects']),
    [addCoursePermission, requirePermissionIfKeyExists('id')],
    body('title')
      .optional({ checkFalsy: false })
      .if(body('title').notEmpty())
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .trim(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID()
      .withMessage('Valid course ID is required'),
    body('projectType')
      .optional({ checkFalsy: false })
      .if(body('projectType').notEmpty())
      .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
      .withMessage('Invalid project type'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('estimatedHours')
      .optional({ checkFalsy: false })
      .if(body('estimatedHours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('totalPoints')
      .optional({ checkFalsy: false })
      .if(body('totalPoints').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('dueDate')
      .optional({ checkFalsy: false })
      .if(body('dueDate').notEmpty())
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('startDate')
      .optional({ checkFalsy: false })
      .if(body('startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .if(body('learning_objectives').notEmpty())
      .isString()
      .trim()
      .withMessage('Learning objectives must be a string'),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .if(body('prerequisites').notEmpty())
      .isString()
      .trim()
      .withMessage('Prerequisites must be a string'),
    body('project_overview')
      .optional({ checkFalsy: false })
      .if(body('project_overview').notEmpty())
      .isString()
      .trim(),
    body('instructions')
      .optional({ checkFalsy: false })
      .if(body('instructions').notEmpty())
      .isString()
      .trim(),
    body('skillsCovered')
      .optional({ checkFalsy: false })
      .if(body('skillsCovered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologiesUsed')
      .optional({ checkFalsy: false })
      .if(body('technologiesUsed').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array'),
    body('isTemplate')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isBoolean()
      .withMessage('isTemplate must be a boolean'),
    body('templateCategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isString()
      .trim()
      .withMessage('Template category must be a string'),
    body('templateSubcategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isString()
      .trim()
      .withMessage('Template subcategory must be a string'),
    body('template_id')
      .optional({ checkFalsy: false })
      .if(body('template_id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required'),
    body('assignments')
      .optional({ checkFalsy: false })
      .if(body('assignments').notEmpty())
      .isArray()
      .withMessage('Assignments must be an array'),
    body('categoryId')
      .optional({ checkFalsy: false })
      .if(body('categoryId').notEmpty())
      .isUUID()
      .withMessage('Valid category ID is required'),
    body('instructorIds')
      .optional({ checkFalsy: false })
      .if(body('instructorIds').notEmpty())
      .isArray()
      .withMessage('instructorIds must be an array of UUIDs'),
    body('instructorIds.*')
      .optional({ checkFalsy: false })
      .if(body('instructorIds.*').notEmpty())
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId').notEmpty())
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId.*').notEmpty())
      .isUUID()
      .withMessage('Each teachingAssId item must be a UUID'),
    body('maxSubmissions')
      .optional({ checkFalsy: false })
      .if(body('maxSubmissions').notEmpty())
      .isInt({ min: 0 })
      .withMessage('maxSubmissions must be an integer'),
    body('lateSubmissionsAllowed')
      .optional({ checkFalsy: false })
      .if(body('lateSubmissionsAllowed').notEmpty())
      .isBoolean()
      .withMessage('lateSubmissionsAllowed must be boolean'),
    body('isScreen')
      .exists({ checkFalsy: true })
      .withMessage('isScreen is required')
      .isInt({ min: 1, max: 4 })
      .withMessage('isScreen must be a number between 1 and 4'),
    body('id')
      .if(body('isScreen').custom(v => Number(v) > 1)) // only run next checks when > 1
      .exists({ checkFalsy: true })
      .withMessage('id is required when isScreen > 1')
      .bail()
      .isUUID()
      .withMessage('id must be a valid UUID'),
    body('sandbox_time_duration')
      .optional({ checkFalsy: false })
      .if(body('sandbox_time_duration').notEmpty())
      .matches(/^(\d{1,3}):([0-5]\d)$/)
      .withMessage(
        'Sandbox duration must be in HHH:MM format (0:00 to 999:59)'
      ),
    body('late_submission_days_allowed')
      .optional({ checkFalsy: false })
      .if(body('late_submission_days_allowed').notEmpty())
      .isInt({ min: 0, max: 365 })
      .withMessage('Late submission days must be between 0 and 365')
  ],
  validate,
  createEnhancedProject
);

//Get all the project
router.get(
  '/enhanced',
  [
    requirePermissions(['view_projects']),
    query('page')
      .optional()
      .if(query('page').notEmpty())
      .isInt({ min: 1 })
      .toInt(),
    query('limit')
      .optional()
      .if(query('limit').notEmpty())
      .isInt({ min: 1, max: 100 })
      .toInt(),
    query('search').optional().if(query('search').notEmpty()).trim(),
    query('courseId').optional().if(query('courseId').notEmpty()).isUUID(),
    query('status')
      .optional()
      .if(query('status').notEmpty())
      .isIn(['draft', 'published', 'archived']),
    query('difficultyLevel')
      .optional()
      .if(query('difficultyLevel').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced']),
    query('isLinked')
      .optional()
      .if(query('isLinked').notEmpty())
      .isBoolean()
      .toBoolean()
  ],
  validate,
  getEnhancedProject
);

// Get project by id with full details
router.get(
  '/:id/details',
  [
    requirePermissions(['project:read']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required')
  ],
  validate,
  getProjectWithDetails
);

// Publish project
router.put(
  '/:id/publish',
  [
    requirePermissions(['publish_projects']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required')
  ],
  validate,
  publishProject
);

// Unpublish project
router.put(
  '/:id/unpublish',
  [
    requirePermissions(['publish_projects']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required')
  ],
  validate,
  unpublishProject
);

// duplicate project
/**
 * @swagger
 * /api/projects/{id}/duplicate:
 *   post:
 *     summary: Duplicate project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - courseId
 *             properties:
 *               courseId:
 *                 type: string
 *                 format: uuid
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *     responses:
 *       201:
 *         description: Project duplicated successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 */
router.post(
  '/:id/duplicate',
  [
    requirePermissions(['create_projects']),
    param('id').if(param('id').notEmpty()).isUUID(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID(),
    body('title')
      .optional({ checkFalsy: false })
      .if(body('title').notEmpty())
      .isLength({ min: 2, max: 200 })
      .trim()
  ],
  validate,
  duplicateProject
);

// Delete project
/**
 * @swagger
 * /api/projects/{id}:
 *   delete:
 *     summary: Delete project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Project deleted successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 *       409:
 *         description: Cannot delete project with submissions
 */
router.delete(
  '/:id',
  [
    requirePermissions(['delete_projects']),
    param('id').if(param('id').notEmpty()).isUUID()
  ],
  validate,
  deleteProject
);

// Create template project
router.post(
  '/templates',
  [
    requirePermissions(['create_projects']),
    /*  body('projectId')
      .exists({ checkFalsy: true })
      .if(body('projectId').notEmpty()) // only run next checks when > 1
      .withMessage('id is required when isScreen > 1')
      .bail()
      .isUUID()
      .withMessage('id must be a valid UUID'), */
    body('title')
      .optional({ checkFalsy: false })
      .if(body('title').notEmpty())
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .trim(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID()
      .withMessage('Valid course ID is required'),
    body('projectType')
      .optional({ checkFalsy: false })
      .if(body('projectType').notEmpty())
      .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
      .withMessage('Invalid project type'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('estimatedHours')
      .optional({ checkFalsy: false })
      .if(body('estimatedHours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('totalPoints')
      .optional({ checkFalsy: false })
      .if(body('totalPoints').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('dueDate')
      .optional({ checkFalsy: false })
      .if(body('dueDate').notEmpty())
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('startDate')
      .optional({ checkFalsy: false })
      .if(body('startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .if(body('learning_objectives').notEmpty())
      .isString()
      .trim()
      .withMessage('Learning objectives must be a string'),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .if(body('prerequisites').notEmpty())
      .isString()
      .trim()
      .withMessage('Prerequisites must be a string'),
    body('project_overview')
      .optional({ checkFalsy: false })
      .if(body('project_overview').notEmpty())
      .isString()
      .trim(),
    body('instructions')
      .optional({ checkFalsy: false })
      .if(body('instructions').notEmpty())
      .isString()
      .trim(),
    body('skillsCovered')
      .optional({ checkFalsy: false })
      .if(body('skillsCovered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologiesUsed')
      .optional({ checkFalsy: false })
      .if(body('technologiesUsed').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array'),
    body('isTemplate')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isBoolean()
      .withMessage('isTemplate must be a boolean'),
    body('templateCategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isString()
      .trim()
      .withMessage('Template category must be a string'),
    body('templateSubcategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isString()
      .trim()
      .withMessage('Template subcategory must be a string'),
    body('instructorIds')
      .optional({ checkFalsy: false })
      .if(body('instructorIds').notEmpty())
      .isArray()
      .withMessage('instructorIds must be an array of UUIDs'),
    body('instructorIds.*')
      .optional({ checkFalsy: false })
      .if(body('instructorIds.*').notEmpty())
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId').notEmpty())
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId.*').notEmpty())
      .isUUID()
      .withMessage('Each teachingAssId item must be a UUID'),
    body('maxSubmissions')
      .optional({ checkFalsy: false })
      .if(body('maxSubmissions').notEmpty())
      .isInt({ min: 0 })
      .withMessage('maxSubmissions must be an integer'),
    body('lateSubmissionsAllowed')
      .optional({ checkFalsy: false })
      .if(body('lateSubmissionsAllowed').notEmpty())
      .isBoolean()
      .withMessage('lateSubmissionsAllowed must be boolean'),
    /*  body('isScreen')
      .exists({ checkFalsy: true })
      .withMessage('isScreen is required')
      .isInt({ min: 1, max: 4 })
      .withMessage('isScreen must be a number between 1 and 4'),
    body('id')
      .if(body('isScreen').custom(v => Number(v) > 1)) // only run next checks when > 1
      .exists({ checkFalsy: true })
      .withMessage('id is required when isScreen > 1')
      .bail()
      .isUUID()
      .withMessage('id must be a valid UUID'), */
    body('sandbox_time_duration')
      .optional({ checkFalsy: false })
      .if(body('sandbox_time_duration').notEmpty())
      .matches(/^(\d{1,3}):([0-5]\d)$/)
      .withMessage(
        'Sandbox duration must be in HHH:MM format (0:00 to 999:59)'
      ),
    body('late_submission_days_allowed')
      .optional({ checkFalsy: false })
      .if(body('late_submission_days_allowed').notEmpty())
      .isInt({ min: 0, max: 365 })
      .withMessage('Late submission days must be between 0 and 365')
  ],
  validate,
  createProjectTemplate
);

// Create template from existing project
router.post(
  '/:projectId/create-template',
  [
    requirePermissions(['edit_projects']),
    param('projectId')
      .if(param('projectId').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required'),
    body('title')
      .exists({ checkFalsy: true })
      .withMessage('Template title is required')
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must not exceed 1000 characters'),
    body('category')
      .optional({ checkFalsy: false })
      .if(body('category').notEmpty())
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Category must be between 2 and 50 characters'),
    body('subcategory')
      .optional({ checkFalsy: false })
      .if(body('subcategory').notEmpty())
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Subcategory must be between 2 and 50 characters'),
    body('skills_covered')
      .optional({ checkFalsy: false })
      .if(body('skills_covered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologies_used')
      .optional({ checkFalsy: false })
      .if(body('technologies_used').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array')
  ],
  validate,
  createTemplateFromProject
);

// Update template project
router.put(
  '/templates/:id',
  [
    requirePermissions(['edit_projects']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required'),
    body('projectId')
      .if(body('projectId').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required'),
    body('title')
      .optional({ checkFalsy: false })
      .if(body('title').notEmpty())
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .trim(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID()
      .withMessage('Valid course ID is required'),
    body('projectType')
      .optional({ checkFalsy: false })
      .if(body('projectType').notEmpty())
      .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
      .withMessage('Invalid project type'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('estimatedHours')
      .optional({ checkFalsy: false })
      .if(body('estimatedHours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('totalPoints')
      .optional({ checkFalsy: false })
      .if(body('totalPoints').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('dueDate')
      .optional({ checkFalsy: false })
      .if(body('dueDate').notEmpty())
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('startDate')
      .optional({ checkFalsy: false })
      .if(body('startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .if(body('learning_objectives').notEmpty())
      .isString()
      .trim()
      .withMessage('Learning objectives must be a string'),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .if(body('prerequisites').notEmpty())
      .isString()
      .trim()
      .withMessage('Prerequisites must be a string'),
    body('project_overview')
      .optional({ checkFalsy: false })
      .if(body('project_overview').notEmpty())
      .isString()
      .trim(),
    body('instructions')
      .optional({ checkFalsy: false })
      .if(body('instructions').notEmpty())
      .isString()
      .trim(),
    body('skillsCovered')
      .optional({ checkFalsy: false })
      .if(body('skillsCovered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologiesUsed')
      .optional({ checkFalsy: false })
      .if(body('technologiesUsed').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array'),
    body('isTemplate')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isBoolean()
      .withMessage('isTemplate must be a boolean'),
    body('templateCategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isString()
      .trim()
      .withMessage('Template category must be a string'),
    body('templateSubcategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isString()
      .trim()
      .withMessage('Template subcategory must be a string'),
    body('instructorIds')
      .optional({ checkFalsy: false })
      .if(body('instructorIds').notEmpty())
      .isArray()
      .withMessage('instructorIds must be an array of UUIDs'),
    body('instructorIds.*')
      .optional({ checkFalsy: false })
      .if(body('instructorIds.*').notEmpty())
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId').notEmpty())
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId.*').notEmpty())
      .isUUID()
      .withMessage('Each teachingAssId item must be a UUID'),
    body('maxSubmissions')
      .optional({ checkFalsy: false })
      .if(body('maxSubmissions').notEmpty())
      .isInt({ min: 0 })
      .withMessage('maxSubmissions must be an integer'),
    body('lateSubmissionsAllowed')
      .optional({ checkFalsy: false })
      .if(body('lateSubmissionsAllowed').notEmpty())
      .isBoolean()
      .withMessage('lateSubmissionsAllowed must be boolean'),
    /*  body('isScreen')
      .exists({ checkFalsy: true })
      .withMessage('isScreen is required')
      .isInt({ min: 1, max: 4 })
      .withMessage('isScreen must be a number between 1 and 4'),
    body('id')
      .if(body('isScreen').custom(v => Number(v) > 1)) // only run next checks when > 1
      .exists({ checkFalsy: true })
      .withMessage('id is required when isScreen > 1')
      .bail()
      .isUUID()
      .withMessage('id must be a valid UUID'), */
    body('sandbox_time_duration')
      .optional({ checkFalsy: false })
      .if(body('sandbox_time_duration').notEmpty())
      .matches(/^(\d{1,3}):([0-5]\d)$/)
      .withMessage(
        'Sandbox duration must be in HHH:MM format (0:00 to 999:59)'
      ),
    body('late_submission_days_allowed')
      .optional({ checkFalsy: false })
      .if(body('late_submission_days_allowed').notEmpty())
      .isInt({ min: 0, max: 365 })
      .withMessage('Late submission days must be between 0 and 365')
  ],
  validate,
  updateProjectTemplate
);

// Get all templates project
router.get(
  '/templates',
  [
    requirePermissions(['view_projects']),
    query('category').optional().if(query('category').notEmpty()).trim(),
    query('subcategory').optional().if(query('subcategory').notEmpty()).trim(),
    query('difficultyLevel')
      .optional()
      .if(query('difficultyLevel').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    query('isFeatured')
      .optional()
      .if(query('isFeatured').notEmpty())
      .isBoolean()
      .toBoolean(),
    query('isPublic')
      .optional()
      .if(query('isPublic').notEmpty())
      .isBoolean()
      .toBoolean(),
    query('page')
      .optional()
      .if(query('page').notEmpty())
      .isInt({ min: 1 })
      .toInt(),
    query('limit')
      .optional()
      .if(query('limit').notEmpty())
      .isInt({ min: 1, max: 100 })
      .toInt(),
    query('search').optional().if(query('search').notEmpty()).trim()
  ],
  validate,
  getProjectTemplates
);

// Get template project by ID
router.get(
  '/templates/:id',
  [
    requirePermissions(['view_projects']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required')
  ],
  validate,
  getTemplateById
);

// Duplicate project from template
router.post(
  '/templates/:id/duplicate',
  [requirePermissions(['project:create'])],
  validate,
  duplicateProjectFromTemplate
);

// Rate project template
router.post(
  '/templates/:id/rate',
  [
    // requirePermissions(['project_template:rate']),
    requirePermissions(['project:create']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required'),
    body('rating')
      .if(body('rating').notEmpty())
      .isFloat({ min: 0, max: 5 })
      .withMessage('Rating must be between 0 and 5')
  ],
  validate,
  rateProjectTemplate
);

// Delete project template
router.delete(
  '/templates/:id',
  [
    requirePermissions(['delete_projects']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required')
  ],
  validate,
  deleteProjectTemplate
);

// Feature/Unfeature project template
router.put(
  '/templates/:id/feature',
  [
    requirePermissions(['admin']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required'),
    body('featured')
      .if(body('featured').notEmpty())
      .isBoolean()
      .withMessage('Featured must be a boolean value')
  ],
  validate,
  featureProjectTemplate
);

// Assign users to project
router.post(
  '/:id/assignments',
  [
    requirePermissions(['project:assign_users']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required'),
    body('assignments')
      .if(body('assignments').notEmpty())
      .isArray({ min: 1 })
      .withMessage(
        'Assignments array is required with at least one assignment'
      ),
    body('assignments.*.userId')
      .if(body('assignments.*.userId').notEmpty())
      .isUUID()
      .withMessage('Each assignment must have a valid user ID'),
    body('assignments.*.role')
      .if(body('assignments.*.role').notEmpty())
      .isIn(['instructor', 'ta', 'reviewer', 'mentor'])
      .withMessage('Each assignment must have a valid role'),
    body('assignments.*.assignmentType')
      .optional({ checkFalsy: false })
      .if(body('assignments.*.assignmentType').notEmpty())
      .isIn(['primary', 'secondary', 'guest'])
      .withMessage('Invalid assignment type'),
    body('assignments.*.startDate')
      .optional({ checkFalsy: false })
      .if(body('assignments.*.startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('assignments.*.endDate')
      .optional({ checkFalsy: false })
      .if(body('assignments.*.endDate').notEmpty())
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
  ],
  validate,
  assignUsersToProject
);

// Get project assignments
router.get(
  '/:id/assignments',
  [
    requirePermissions(['project:view_assignments']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required')
  ],
  validate,
  getProjectAssignments
);

// Remove user assignment from project
router.delete(
  '/:id/assignments/:userId',
  [
    requirePermissions(['project:manage_assignments']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required'),
    param('userId')
      .if(param('userId').notEmpty())
      .isUUID()
      .withMessage('Valid user ID is required')
  ],
  validate,
  removeUserAssignment
);

// Save project as draft
router.post(
  '/:id/save-draft',
  [
    requirePermissions(['project:update']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required')
  ],
  validate,
  saveProjectAsDraft
);

// Assignment integration routes
router.get(
  '/assignments/user/:role?',
  [
    requirePermissions(['project:read']),
    param('role')
      .optional({ checkFalsy: false })
      .if(param('role').notEmpty())
      .isIn(['instructor', 'ta', 'reviewer', 'mentor'])
      .withMessage('Invalid role. Must be instructor, ta, reviewer, or mentor')
  ],
  validate,
  getProjectsByUserAssignment
);

//getUserProjectWorkload
router.get(
  '/workload',
  [requirePermissions(['project:read'])],
  getUserProjectWorkload
);

//getProjectAssignmentStats
router.get(
  '/:id/assignment-stats',
  [
    requirePermissions(['project:view_assignments']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required')
  ],
  validate,
  getProjectAssignmentStats
);

// Update enhanced project
router.put(
  '/:id/enhanced',
  [
    requirePermissions(['edit_projects']),
    param('id')
      .if(param('id').notEmpty())
      .isUUID()
      .withMessage('Valid project ID is required'),
    body('title')
      .optional({ checkFalsy: false })
      .if(body('title').notEmpty())
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .trim(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID()
      .withMessage('Valid course ID is required'),
    body('projectType')
      .optional({ checkFalsy: false })
      .if(body('projectType').notEmpty())
      .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
      .withMessage('Invalid project type'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('estimatedHours')
      .optional({ checkFalsy: false })
      .if(body('estimatedHours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('totalPoints')
      .optional({ checkFalsy: false })
      .if(body('totalPoints').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('dueDate')
      .optional({ checkFalsy: false })
      .if(body('dueDate').notEmpty())
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('startDate')
      .optional({ checkFalsy: false })
      .if(body('startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .if(body('learning_objectives').notEmpty())
      .isString()
      .trim(),
    body('instructions')
      .optional({ checkFalsy: false })
      .if(body('instructions').notEmpty())
      .isString()
      .trim(),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .if(body('prerequisites').notEmpty())
      .isString()
      .trim(),
    body('project_overview')
      .optional({ checkFalsy: false })
      .if(body('project_overview').notEmpty())
      .isString()
      .trim(),
    body('skillsCovered')
      .optional({ checkFalsy: false })
      .if(body('skillsCovered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologiesUsed')
      .optional({ checkFalsy: false })
      .if(body('technologiesUsed').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array'),
    body('isTemplate')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isBoolean()
      .withMessage('isTemplate must be a boolean'),
    body('assignments')
      .optional({ checkFalsy: false })
      .if(body('assignments').notEmpty())
      .isArray()
      .withMessage('Assignments must be an array'),
    body('categoryId')
      .optional({ checkFalsy: false })
      .if(body('categoryId').notEmpty())
      .isUUID()
      .withMessage('Valid category ID is required'),
    body('instructorIds')
      .optional({ checkFalsy: false })
      .if(body('instructorIds').notEmpty())
      .isArray()
      .withMessage('instructorIds must be an array of UUIDs'),
    body('instructorIds.*')
      .optional({ checkFalsy: false })
      .if(body('instructorIds.*').notEmpty())
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId').notEmpty())
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId.*').notEmpty())
      .isUUID()
      .withMessage('Each teachingAssId item must be a UUID'),
    body('maxSubmissions')
      .optional({ checkFalsy: false })
      .if(body('maxSubmissions').notEmpty())
      .isInt({ min: 0 })
      .withMessage('maxSubmissions must be an integer'),
    body('lateSubmissionsAllowed')
      .optional({ checkFalsy: false })
      .if(body('lateSubmissionsAllowed').notEmpty())
      .isBoolean()
      .withMessage('lateSubmissionsAllowed must be boolean'),
    body('sandbox_time_duration')
      .optional({ checkFalsy: false })
      .if(body('sandbox_time_duration').notEmpty())
      .matches(/^(\d{1,3}):([0-5]\d)$/)
      .withMessage(
        'Sandbox duration must be in HHH:MM format (0:00 to 999:59)'
      ),
    body('late_submission_days_allowed')
      .optional({ checkFalsy: false })
      .if(body('late_submission_days_allowed').notEmpty())
      .isInt({ min: 0, max: 365 })
      .withMessage('Late submission days must be between 0 and 365')
  ],
  validate,
  updateEnhancedProject
);

export default router;
