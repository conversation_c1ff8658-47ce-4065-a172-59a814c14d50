import express from 'express';
import {
  getLTIConfiguration,
  initiateLogin,
  handleOID<PERSON>allback,
  handleLaunch,
  getJWKS,
  handleDeepLinking
} from '../controllers/lti.controller.js';
import { body } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: LTI Launch
 *   description: LTI 1.3 launch and configuration endpoints
 */

/**
 * @swagger
 * /api/lti/config:
 *   get:
 *     summary: Get LTI tool configuration for platform registration
 *     tags: [LTI Launch]
 *     responses:
 *       200:
 *         description: LTI configuration retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 title:
 *                   type: string
 *                 description:
 *                   type: string
 *                 target_link_uri:
 *                   type: string
 *                 oidc_initiation_url:
 *                   type: string
 *                 public_jwk_url:
 *                   type: string
 *                 scopes:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get('/config', getLTIConfiguration);

/**
 * @swagger
 * /api/lti/login:
 *   post:
 *     summary: LTI login initiation endpoint
 *     tags: [LTI Launch]
 *     description: Initiates LTI 1.3 authentication flow
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - iss
 *               - login_hint
 *               - target_link_uri
 *               - client_id
 *             properties:
 *               iss:
 *                 type: string
 *                 description: Platform issuer identifier
 *               login_hint:
 *                 type: string
 *                 description: User identifier hint from platform
 *               target_link_uri:
 *                 type: string
 *                 description: Target URI for the launch
 *               client_id:
 *                 type: string
 *                 description: OAuth client ID
 *               lti_message_hint:
 *                 type: string
 *                 description: Optional message hint
 *     responses:
 *       302:
 *         description: Redirect to platform authentication
 *       400:
 *         description: Invalid platform or parameters
 */
router.post(
  '/oidc/init',
  [
    body('iss').isURL(),
    body('login_hint').isString(),
    body('target_link_uri').isURL(),
    body('client_id').isString(),
    body('lti_message_hint').optional().isString()
  ],
  validate,
  initiateLogin
);

/**
 * @swagger
 * /api/lti/oidc/callback:
 *   post:
 *     summary: LTI OIDC callback endpoint
 *     tags: [LTI Launch]
 *     description: Handles OIDC callback from platform with ID token
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token from platform
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       302:
 *         description: Redirect to application page
 *       400:
 *         description: Invalid callback parameters or token
 */
router.post(
  '/oidc/callback',
  [body('id_token').isJWT(), body('state').isString()],
  validate,
  handleOIDCCallback
);

/**
 * @swagger
 * /api/lti/launch:
 *   post:
 *     summary: LTI launch endpoint
 *     tags: [LTI Launch]
 *     description: Handles LTI 1.3 launch requests with ID token
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token from platform
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       302:
 *         description: Redirect to application page
 *       400:
 *         description: Invalid launch parameters or token
 */
router.post(
  '/launch',
  [body('id_token').isJWT(), body('state').isString()],
  validate,
  handleLaunch
);

/**
 * @swagger
 * /api/lti/jwks:
 *   get:
 *     summary: Get tool's public key set (JWKS)
 *     tags: [LTI Launch]
 *     description: Provides public keys for JWT verification
 *     responses:
 *       200:
 *         description: JWKS retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 keys:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       kty:
 *                         type: string
 *                       use:
 *                         type: string
 *                       kid:
 *                         type: string
 *                       alg:
 *                         type: string
 *                       n:
 *                         type: string
 *                       e:
 *                         type: string
 */
router.get('/jwks', getJWKS);

/**
 * @swagger
 * /api/lti/deep-linking:
 *   post:
 *     summary: Handle deep linking requests
 *     tags: [LTI Launch]
 *     description: Processes LTI deep linking for content selection
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token with deep linking claim
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       200:
 *         description: Deep linking response form
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *       400:
 *         description: Invalid deep linking request
 */
router.post(
  '/deep-linking',
  [body('id_token').isJWT(), body('state').isString()],
  validate,
  handleDeepLinking
);

export default router;