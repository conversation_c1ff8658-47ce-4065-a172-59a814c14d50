import express from 'express';
import { body, param, query } from 'express-validator';
import multer from 'multer';
import {
  autoSaveSubmission,
  createInstructorReviewSandbox,
  createOrUpdateSubmission,
  downloadSubmissionNotebook,
  getAllCheckpoints,
  getCheckpointProgressById,
  getSubmissionByInstructorId,
  getSubmissionByProjectId,
  getSubmissionByUserIdAndProjectId,
  getSubmissionStatistics,
  submitAssignment
} from '../controllers/submission.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { attachJupyterToken } from '../middlewares/jupyterhub.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow specific file types
    const allowedMimes = [
      'text/plain',
      'application/json',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed',
      'application/octet-stream',
      'image/png',
      'image/jpeg',
      'image/gif',
      'application/pdf',
      'application/ipynb'
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ApiError(httpStatus.BAD_REQUEST, 'Invalid file type'), false);
    }
  }
});

// 📌 Get submissions
router.get(
  '/',
  [
    requirePermissions(['view_submissions']),
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('projectId').optional().isUUID(),
    query('userId').optional().isUUID(),
    query('status')
      .optional()
      .isIn(['in_progress', 'submitted', 'grading', 'graded', 'returned'])
  ],
  validate,
  getAllCheckpoints
);

// 📌 Get single submission
router.get('/:id', [
  param('id').isUUID()
], validate, getCheckpointProgressById);

router.get(
  '/instructor/my-submissions',
  [
    requirePermissions(['view_submissions']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status')
      .optional()
      .isIn([
        'submitted',
        'in_progress',
        'draft',
        'late',
        'pending_review',
        'graded'
      ]),
    query('sortBy')
      .optional()
      .isIn(['submitted_at', 'status', 'user_name', 'project_title']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  validate,
  getSubmissionByInstructorId
);

router.get(
  '/users/:userId/projects/:projectId',
  [
    requirePermissions(['view_submissions']),
    param('userId').isUUID(),
    param('projectId').isUUID()
  ],
  validate,
  getSubmissionByUserIdAndProjectId
);

router.get(
  '/projects/:projectId',
  [
    requirePermissions(['view_submissions']),
    param('projectId').isUUID(),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status')
      .optional()
      .isIn(['draft', 'submitted', 'graded', 'returned']),
    query('sortBy')
      .optional()
      .isIn(['submitted_at', 'status', 'user_name', 'created_at']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  validate,
  getSubmissionByProjectId
);

router.get(
  '/checkpoint-progress/:id',
  [param('id').isUUID()],
  validate,
  getCheckpointProgressById
);

router.post(
  '/checkpoint-progress/:id/create-review-sandbox',
  [
    jwtMiddleware,
    attachJupyterToken,
    param('id')
      .isUUID()
      .withMessage('A valid checkpoint progress ID is required.'),
    validate
  ],
  createInstructorReviewSandbox
);

// 📌 Create or update submission
router.post(
  '/',
  [
    requirePermissions(['submit_assignments']), // Uncomment for production
    body('projectId')
      .isUUID()
      .trim()
      .withMessage('Valid project ID is required'),
    body('checkpointId')
      .isUUID()
      .trim()
      .withMessage('Valid checkpoint ID is required'),
    body('submissionSummary')
      .optional()
      .isString()
      .withMessage('Submission summary must be a string'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object'),
    body('timeSpent')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Time spent must be a positive number'),
    body('max_attempts')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Max attempts is required'),
    body('filenamesToSubmit')
      .optional()
      .isArray()
      .withMessage('filenamesToSubmit must be an array.')
    // upload.array('files', 20)
  ],
  validate,
  createOrUpdateSubmission
);

router.put(
  '/',
  [
    requirePermissions(['submit_assignments']), // Uncomment for production
    body('projectId')
      .isUUID()
      .trim()
      .withMessage('Valid project ID is required'),
    body('submissionSummary')
      .optional()
      .isString()
      .withMessage('Submission summary must be a string'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object'),
    body('timeSpent')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Time spent must be a positive number'),
    upload.array('files', 20)
  ],
  createOrUpdateSubmission
);

// 📌 Auto-save submission
router.post(
  '/autosave',
  [
    requirePermissions(['submit_assignments']),
    body('projectId').isUUID(),
    body('notebookContent').isString(),
    body('metadata').optional().isObject()
  ],
  validate,
  autoSaveSubmission
);

// 📌 Final submit
router.post(
  '/:id/submit',
  [requirePermissions(['submit_assignments']), param('id').isUUID()],
  validate,
  submitAssignment
);

// 📌 Download files
router.get(
  '/:id/download',
  [param('id').isUUID()],
  validate,
  downloadSubmissionNotebook
);

// 📌 Get project statistics
router.get(
  '/project/:projectId/statistics',
  [requirePermissions(['view_submissions']), param('projectId').isUUID()],
  validate,
  getSubmissionStatistics
);

export default router;

/**
 * @swagger
 * tags:
 *   name: Submissions
 *   description: Student submission management
 */

/**
 * @swagger
 * /api/submissions:
 *   get:
 *     summary: Get all submissions with pagination and filtering
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by user ID (admin/instructor only)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [in_progress, submitted, grading, graded, returned]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Submissions retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */

/**
 * @swagger
 * /api/submissions/{id}:
 *   get:
 *     summary: Get submission by ID
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Submission retrieved successfully
 *       404:
 *         description: Submission not found
 *       403:
 *         description: Access denied
 */

/**
 * @swagger
 * /api/submissions:
 *   post:
 *     summary: Create or update submission
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *             properties:
 *               projectId:
 *                 type: string
 *                 format: uuid
 *               notebookContent:
 *                 type: string
 *                 description: Jupyter notebook content as JSON string
 *               metadata:
 *                 type: object
 *                 description: Additional metadata for the submission
 *     responses:
 *       200:
 *         description: Submission saved successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 *       404:
 *         description: Project not found
 */

/**
 * @swagger
 * /api/submissions/autosave:
 *   post:
 *     summary: Auto-save submission (periodic saves)
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *               - notebookContent
 *             properties:
 *               projectId:
 *                 type: string
 *                 format: uuid
 *               notebookContent:
 *                 type: string
 *                 description: Jupyter notebook content as JSON string
 *               metadata:
 *                 type: object
 *                 description: Additional metadata for the submission
 *     responses:
 *       200:
 *         description: Auto-save successful
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 */

/**
 * @swagger
 * /api/submissions/{id}/submit:
 *   post:
 *     summary: Submit assignment (finalize submission)
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Assignment submitted successfully
 *       400:
 *         description: Validation error or already submitted
 *       403:
 *         description: Access denied
 *       404:
 *         description: Submission not found
 */

/**
 * @swagger
 * /api/submissions/{id}/download:
 *   get:
 *     summary: Download submission notebook
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Download URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 downloadUrl:
 *                   type: string
 *                   format: uri
 *                 fileName:
 *                   type: string
 *                 expiresIn:
 *                   type: integer
 *       403:
 *         description: Access denied
 *       404:
 *         description: Submission or notebook not found
 */

/**
 * @swagger
 * /api/submissions/project/{projectId}/statistics:
 *   get:
 *     summary: Get submission statistics for a project
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Project not found
 */
