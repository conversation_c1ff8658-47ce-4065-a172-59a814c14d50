import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Submission = sequelize.define('Submission', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  status: {
    type: DataTypes.ENUM('in_progress', 'submitted', 'grading', 'graded', 'returned'),
    defaultValue: 'in_progress'
  },
  submitted_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  late_submission: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  time_spent: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  execution_time: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  execution_output: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  submission_summary: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional submission metadata'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'submissions',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'project_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['submitted_at']
    },
    {
      fields: ['late_submission']
    }
  ]
});

export default Submission;
