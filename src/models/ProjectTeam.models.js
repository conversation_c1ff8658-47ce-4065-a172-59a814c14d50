import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const ProjectTeam = sequelize.define('ProjectTeam', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    },
    comment: 'Project ID'
  },
  team_name: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Optional team name'
  },
  team_leader_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Team leader user ID'
  },
  status: {
    type: DataTypes.ENUM('forming', 'active', 'completed', 'disbanded'),
    defaultValue: 'forming',
    comment: 'Team status'
  },
  max_size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: 'Maximum team size'
  },
  current_size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Current team size'
  },
  formation_deadline: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Team formation deadline'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional team metadata'
  }
}, {
  tableName: 'project_teams',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['project_id']
    },
    {
      fields: ['team_leader_id']
    },
    {
      fields: ['status']
    },
    {
      unique: true,
      fields: ['project_id', 'team_name']
    }
  ]
});

export default ProjectTeam;
