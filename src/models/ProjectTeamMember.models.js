import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const ProjectTeamMember = sequelize.define('ProjectTeamMember', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  team_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project_teams',
      key: 'id'
    },
    comment: 'Team ID'
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User ID'
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    },
    comment: 'Project ID'
  },
  role: {
    type: DataTypes.ENUM('leader', 'member'),
    defaultValue: 'member',
    comment: 'Role in the team'
  },
  status: {
    type: DataTypes.ENUM('invited', 'accepted', 'declined', 'removed'),
    defaultValue: 'invited',
    comment: 'Membership status'
  },
  joined_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the user joined the team'
  },
  invited_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When the user was invited'
  },
  invited_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who sent the invitation'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional membership metadata'
  }
}, {
  tableName: 'project_team_members',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['team_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['status']
    },
    {
      unique: true,
      fields: ['team_id', 'user_id']
    },
    {
      unique: true,
      fields: ['project_id', 'user_id']
    }
  ]
});

export default ProjectTeamMember;
