'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('checkpoints', 'is_template', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this checkpoint can be used as a template'
    });

    await queryInterface.addColumn('checkpoints', 'template_name', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'Name for template checkpoint'
    });

    await queryInterface.addColumn('checkpoints', 'is_deleted', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Flag to indicate if the project is deleted'
    });

    await queryInterface.addColumn('checkpoints', 'deleted_by', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('checkpoints', 'deleted_at', {
      type: Sequelize.DATE,
      allowNull: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('checkpoints', 'is_template');
    await queryInterface.removeColumn('checkpoints', 'template_name');
    await queryInterface.removeColumn('checkpoints', 'is_deleted');
    await queryInterface.removeColumn('checkpoints', 'deleted_by');
    await queryInterface.removeColumn('checkpoints', 'deleted_at');
  }
};
