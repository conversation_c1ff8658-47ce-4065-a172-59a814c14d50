'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('rubrics', 'is_deleted', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Flag to indicate if the rubric is deleted'
    });

    await queryInterface.addColumn('rubrics', 'deleted_by', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('rubrics', 'deleted_at', {
      type: Sequelize.DATE,
      allowNull: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('rubrics', 'is_deleted');
    await queryInterface.removeColumn('rubrics', 'deleted_by');
    await queryInterface.removeColumn('rubrics', 'deleted_at');
  }
};
