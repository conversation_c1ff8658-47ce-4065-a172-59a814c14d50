import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  // Add new fields to projects table
  await queryInterface.addColumn('projects', 'total_points', {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 100,
    comment: 'Total points available for this project'
  });

  await queryInterface.addColumn('projects', 'is_team_project', {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this is a team project'
  });

  await queryInterface.addColumn('projects', 'max_team_size', {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 1,
    comment: 'Maximum team size for team projects'
  });

  await queryInterface.addColumn('projects', 'team_formation_deadline', {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Deadline for team formation'
  });

  // Create project_teams table
  await queryInterface.createTable('project_teams', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    project_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'projects',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Project ID'
    },
    team_name: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Optional team name'
    },
    team_leader_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'Team leader user ID'
    },
    status: {
      type: DataTypes.ENUM('forming', 'active', 'completed', 'disbanded'),
      defaultValue: 'forming',
      comment: 'Team status'
    },
    max_size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Maximum team size'
    },
    current_size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Current team size'
    },
    formation_deadline: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Team formation deadline'
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Additional team metadata'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  });

  // Create indexes for project_teams
  await queryInterface.addIndex('project_teams', ['project_id']);
  await queryInterface.addIndex('project_teams', ['team_leader_id']);
  await queryInterface.addIndex('project_teams', ['status']);
  await queryInterface.addIndex('project_teams', ['project_id', 'team_name'], {
    unique: true,
    name: 'project_teams_project_team_name_unique'
  });

  // Create project_team_members table
  await queryInterface.createTable('project_team_members', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    team_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'project_teams',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Team ID'
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'User ID'
    },
    project_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'projects',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Project ID'
    },
    role: {
      type: DataTypes.ENUM('leader', 'member'),
      defaultValue: 'member',
      comment: 'Role in the team'
    },
    status: {
      type: DataTypes.ENUM('invited', 'accepted', 'declined', 'removed'),
      defaultValue: 'invited',
      comment: 'Membership status'
    },
    joined_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the user joined the team'
    },
    invited_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'When the user was invited'
    },
    invited_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User who sent the invitation'
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Additional membership metadata'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  });

  // Create indexes for project_team_members
  await queryInterface.addIndex('project_team_members', ['team_id']);
  await queryInterface.addIndex('project_team_members', ['user_id']);
  await queryInterface.addIndex('project_team_members', ['project_id']);
  await queryInterface.addIndex('project_team_members', ['status']);
  await queryInterface.addIndex('project_team_members', ['team_id', 'user_id'], {
    unique: true,
    name: 'project_team_members_team_user_unique'
  });
  await queryInterface.addIndex('project_team_members', ['project_id', 'user_id'], {
    unique: true,
    name: 'project_team_members_project_user_unique'
  });
};

export const down = async (queryInterface, Sequelize) => {
  // Drop project_team_members table
  await queryInterface.dropTable('project_team_members');

  // Drop project_teams table
  await queryInterface.dropTable('project_teams');

  // Remove columns from projects table
  await queryInterface.removeColumn('projects', 'team_formation_deadline');
  await queryInterface.removeColumn('projects', 'max_team_size');
  await queryInterface.removeColumn('projects', 'is_team_project');
  await queryInterface.removeColumn('projects', 'total_points');
};
