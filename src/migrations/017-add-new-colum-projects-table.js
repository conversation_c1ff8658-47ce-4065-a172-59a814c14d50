'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('projects', 'is_team_project', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is a team project'
    });

    await queryInterface.addColumn('projects', 'max_team_size', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 1,
      comment: 'Maximum team size for team projects'
    });

    await queryInterface.addColumn('projects', 'team_formation_deadline', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'Deadline for team formation'
    });

    await queryInterface.addColumn('projects', 'is_template', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this project can be used as a template'
    });

    await queryInterface.addColumn('projects', 'template_name', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'Name for template project'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('projects', 'is_team_project');
    await queryInterface.removeColumn('projects', 'max_team_size');
    await queryInterface.removeColumn('projects', 'team_formation_deadline');
  }
};
