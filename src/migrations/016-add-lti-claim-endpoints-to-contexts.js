'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Add AGS claim endpoint column to store LTI Assignment and Grade Services endpoint data
    await queryInterface.addColumn('lti_contexts', 'ags_claim_endpoint', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: null,
      comment: 'LTI AGS (Assignment and Grade Services) claim endpoint data from launch payload'
    });

    // Add NRPS claim endpoint column to store LTI Names and Role Provisioning Services endpoint data
    await queryInterface.addColumn('lti_contexts', 'nrps_claim_endpoint', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: null,
      comment: 'LTI NRPS (Names and Role Provisioning Services) claim endpoint data from launch payload'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove the columns in reverse order for clean rollback
    await queryInterface.removeColumn('lti_contexts', 'nrps_claim_endpoint');
    await queryInterface.removeColumn('lti_contexts', 'ags_claim_endpoint');
  }
};