import httpStatus from 'http-status';
import { Op } from 'sequelize';
import config, { sequelize } from '../config/database.config.js';
import logger from '../config/logger.config.js';
import {
  Checkpoint,
  CheckpointGrade,
  CheckpointProgress,
  Grade,
  LtiContext,
  LtiContextEnrollment,
  Project,
  StudentProjectProgress,
  Submission,
  User
} from '../models/associations.js';
import ApiError from '../utils/ApiError.utils.js';
import { LoggerError } from '../utils/helpers.utils.js';
import jupyterService from './jupyterhub.service.js';
import { default as S3Service } from './s3.service.js';

class SubmissionService {
  //     /**
  //      * Get submissions with filters and pagination
  //      */
  static async getSubmissions(req) {
    try {
      const {
        page = 1,
        limit = 10,
        projectId,
        status,
        sortBy = 'submitted_at',
        sortOrder = 'desc'
      } = req.query;

      // console.log('request details \n:',req);

      const userId = req.user.id;
      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);
      const offset = (pageNum - 1) * limitNum;

      const whereClause = {};

      // Role-based access
      if (req.userRoles?.includes('admin')) {
        // Admin can see everything (no restrictions)
        if (projectId) whereClause.project_id = projectId;
        if (userId) whereClause.user_id = userId;
      } else if (req.userRoles?.includes('instructor')) {
        // Instructor: Get only submissions for projects owned by them
        const instructorProjects = await Project.findAll({
          where: {
            instructor_id: { [Op.contains]: [userId] }
          },
          attributes: ['id'],
          raw: true
        });
        console.log('Instructor Proj', instructorProjects);
        // Extract project IDs into a plain array
        const instructorProjectIds = instructorProjects.map(p => p.id);

        if (instructorProjectIds.length === 0) {
          whereClause.id = null; // Force query to return nothing
        } else if (projectId) {
          if (instructorProjectIds.includes(projectId)) {
            whereClause.project_id = projectId;
          } else {
            throw new ApiError(
              httpStatus.FORBIDDEN,
              'Access to this project is denied'
            );
          }
        } else {
          whereClause.project_id = { [Op.in]: instructorProjectIds };
        }
      } else if (req.userRoles?.includes('student')) {
        // Student: Only see their own submissions
        whereClause.user_id = req.user.id;
        console.log('Final whereClause:', whereClause);

        if (projectId) whereClause.project_id = projectId; // student filter still applies
      } else {
        throw new ApiError(httpStatus.FORBIDDEN, 'Unauthorized access');
      }

      if (status && status !== 'in_progress') {
        whereClause.status = status;
      }

      const { count, rows } = await Submission.findAndCountAll({
        where: whereClause,
        include: this.getSubmissionIncludes(),
        limit: limitNum,
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      return {
        submissions: this.transformSubmissions(rows),
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: limitNum
        }
      };
    } catch (error) {
      console.error('Error fetching submissions:', error);
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to fetch submissions'
      );
    }
  }

  static async getAllCheckpoints(req) {
  try {
    const {
      page = 1,
      limit = 10,
      projectId,
      status,
      sortBy = 'submitted_at',
      sortOrder = 'desc'
    } = req.query;

    const userId = req.user.id;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const offset = (pageNum - 1) * limitNum;

    let whereClause = {};

    // Role-based access control
    if (req.userRoles?.includes('admin')) {
      // Admin: see all checkpoint progress
      if (projectId) whereClause.project_id = projectId;
      if (status) whereClause.status = status;
    } else if (req.userRoles?.includes('instructor')) {
      // Instructor: only their projects
      const instructorProjects = await Project.findAll({
        where: { instructor_id: { [Op.contains]: [userId] } },
        attributes: ['id'],
        raw: true
      });

      const instructorProjectIds = instructorProjects.map(p => p.id);
      
      if (instructorProjectIds.length === 0) {
        return {
          checkpointProgress: [],
          pagination: { currentPage: pageNum, 
                        totalPages: 0, 
                        totalItems: 0, 
                        itemsPerPage: limitNum }
        };
      }

      whereClause.project_id = { [Op.in]: instructorProjectIds };
      if (projectId && instructorProjectIds.includes(projectId)) {
        whereClause.project_id = projectId;
      }
      if (status) whereClause.status = status;
    } else if (req.userRoles?.includes('student')) {
      // Student: only their own records
      whereClause.user_id = userId;
      if (projectId) whereClause.project_id = projectId;
      if (status) whereClause.status = status;
    } else {
      throw new ApiError(httpStatus.FORBIDDEN, 'Unauthorized access');
    }

    const { count, rows } = await CheckpointProgress.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'profile_picture']
        },
        {
          model: Checkpoint,
          as: 'checkpoint',
          attributes: ['id', 'title', 'description', 'checkpoint_number', 'due_date', 'weight_percentage']
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'due_date', 'difficulty_level'],
          include: [
            {
              model: LtiContext,
              as: 'course',
              attributes: [
                'id',
                ['context_id', 'course_id'],
                ['context_title', 'name'],
                ['context_label', 'code']
              ]
            }
          ]
        },
        {
          model: CheckpointGrade,
          as: 'grade',
          required: false,
          include: [
            {
              model: User,
              as: 'evaluator',
              attributes: ['id', 'name', 'email']
            }
          ]
        }
      ],
      limit: limitNum,
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    const checkpointProgress = rows.map(progress => ({
      id: progress.id,
      status: progress.status,
      submittedAt: progress.submitted_at,
      executionTime: null,
      timeSpent: progress.time_spent_minutes || 0,
      attempts: progress.attempts,
      submissionSummary: progress.student_notes,
      metadata: progress.metadata || {},
      user: progress.user ? {
        id: progress.user.id,
        name: progress.user.name,
        email: progress.user.email,
        profilePicture: progress.user.profile_picture
      } : null,
      checkpoint: progress.checkpoint ? {
        id: progress.checkpoint.id,
        title: progress.checkpoint.title,
        description: progress.checkpoint.description,
        checkpointNumber: progress.checkpoint.checkpoint_number,
        dueDate: progress.checkpoint.due_date,
        weightPercentage: progress.checkpoint.weight_percentage
      } : null,
      project: progress.project ? {
        id: progress.project.id,
        title: progress.project.title,
        dueDate: progress.project.due_date,
        difficultyLevel: progress.project.difficulty_level,
        course: progress.project.course ? {
          id: progress.project.course.id,
          course_id: progress.project.course.course_id,
          name: progress.project.course.name,
          code: progress.project.course.code
        } : null
      } : null,
      grade: progress.grade ? {
        id: progress.grade.id,
        totalScore: progress.grade.total_score,
        maxScore: progress.grade.max_score,
        percentage: progress.grade.percentage,
        letterGrade: progress.grade.letter_grade,
        feedback: progress.grade.feedback,
        evaluator: progress.grade.evaluator ? {
          id: progress.grade.evaluator.id,
          name: progress.grade.evaluator.name,
          email: progress.grade.evaluator.email
        } : null,
        gradedAt: progress.grade.graded_at
      } : null,
      createdAt: progress.created_at,
      updatedAt: progress.updated_at
    }));

    return {
      checkpointProgress,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(count / limitNum),
        totalItems: count,
        itemsPerPage: limitNum
      }
    };
  } catch (error) {
    throw error;
  }
}

  //     /**
  //      * Get submission by ID
  //      */
  static async getSubmissionById(req) {
    try {
      const { id } = req.params;
      console.log('request details \n:', req.userRoles?.includes('instructor'));
      const submission = await Submission.findByPk(id, {
        include: this.getSubmissionIncludes()
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      const isAdmin = req.userRoles?.includes('admin');
      const isOwner = submission.user_id === req.user.id;
      // const isInstructor = submission.project?.course?.instructor_id === req.user.id;
      const isInstructor = req.userRoles?.includes('instructor');
      const hasAccess = isAdmin || isOwner || isInstructor;

      if (!hasAccess) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to view this submission'
        );
      }

      let notebookPresignedUrls = [];
      if (submission.metadata?.notebooks?.length) {
        try {
          notebookPresignedUrls = await Promise.all(
            submission.metadata.notebooks.map(async nb => {
              try {
                const url = await S3Service.generatePresignedDownloadUrl(
                  nb.key,
                  3600
                );
                return { ...nb, presignedUrl: url };
              } catch (err) {
                LoggerError(
                  req,
                  `Error generating presigned URL for ${nb.key}`,
                  'getSubmissionById',
                  httpStatus.INTERNAL_SERVER_ERROR,
                  err
                );
                return { ...nb, presignedUrl: null };
              }
            })
          );
        } catch (error) {
          LoggerError(
            req,
            'Error generating presigned URL',
            'getSubmissionById',
            httpStatus.INTERNAL_SERVER_ERROR,
            error
          );
        }
      }

      return {
        ...this.transformSubmission(submission),
        notebookPresignedUrls
      };
    } catch (error) {
      // Ensure standardized error handling
      if (error instanceof ApiError) throw error;

      LoggerError(
        req,
        'Unexpected error in getSubmissionById',
        'getSubmissionById',
        httpStatus.INTERNAL_SERVER_ERROR,
        error
      );

      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to retrieve submission'
      );
    }
  }


/**
 * Get checkpoint progress by ID with submission-like response structure
 */
static async getCheckpointProgressById(req) {
  try {
    const { id } = req.params;

    const checkpointProgress = await CheckpointProgress.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'profile_picture']
        },
        {
          model: Checkpoint,
          as: 'checkpoint',
          attributes: ['id', 'title', 'description', 'checkpoint_number', 'due_date'],
          include: [
            {
              model: Project,
              as: 'project',
              attributes: ['id', 'title', 'due_date', 'difficulty_level'],
              include: [
                {
                  model: LtiContext,
                  as: 'course',
                  attributes: ['id', ['context_title', 'name'], ['context_label', 'code']]
                }
              ]
            }
          ]
        }
      ]
    });

    if (!checkpointProgress) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Checkpoint progress not found');
    }

    const isAdmin = req.userRoles?.includes('admin');
    const isOwner = checkpointProgress.user_id === req.user.id;
    const isInstructor = req.userRoles?.includes('instructor');
    const hasAccess = isAdmin || isOwner || isInstructor;

    if (!hasAccess) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to view this checkpoint progress');
    }

    let filesWithUrls = [];
    const filesSubmitted = checkpointProgress.files_submitted || [];
    
    if (Array.isArray(filesSubmitted) && filesSubmitted.length > 0) {
      filesWithUrls = await Promise.all(
        filesSubmitted.map(async (file) => {
          try {
            const presignedUrl = await S3Service.generatePresignedDownloadUrl(file.key, 3600);
            return { ...file, presignedUrl };
          } catch (err) {
            return { ...file, presignedUrl: null };
          }
        })
      );
    }

    return {
      id: checkpointProgress.id,
      status: checkpointProgress.status,
      submittedAt: checkpointProgress.submitted_at,
      executionTime: null,
      timeSpent: checkpointProgress.time_spent_minutes || 0,
      attempts: checkpointProgress.attempts,
      submissionSummary: checkpointProgress.student_notes,
      metadata: checkpointProgress.metadata || {},
      user: {
        id: checkpointProgress.user.id,
        name: checkpointProgress.user.name,
        email: checkpointProgress.user.email,
        profilePicture: checkpointProgress.user.profile_picture
      },
      project: {
        id: checkpointProgress.checkpoint.project.id,
        title: checkpointProgress.checkpoint.project.title,
        dueDate: checkpointProgress.checkpoint.project.due_date,
        difficultyLevel: checkpointProgress.checkpoint.project.difficulty_level,
        course: {
          id: checkpointProgress.checkpoint.project.course.id,
          name: checkpointProgress.checkpoint.project.course.name,
          code: checkpointProgress.checkpoint.project.course.code
        }
      },
      checkpoint: {
        id: checkpointProgress.checkpoint.id,
        title: checkpointProgress.checkpoint.title,
        description: checkpointProgress.checkpoint.description,
        checkpointNumber: checkpointProgress.checkpoint.checkpoint_number,
        dueDate: checkpointProgress.checkpoint.due_date
      },
      grade: null,
      filesSubmitted: filesWithUrls,
      filesCount: filesSubmitted.length,
      createdAt: checkpointProgress.created_at,
      updatedAt: checkpointProgress.updated_at
    };

  } catch (error) {
    throw error;
  }
}


  /**
   * Get submission by Instructor ID
   */
  static async getSubmissionByInstructorId(req) {
    try {
      // 1. Get instructor user ID from session
      const instructorId = req.session?.user?.id || req.user?.id;
      if (!instructorId) {
        throw new ApiError(
          httpStatus.UNAUTHORIZED,
          'Instructor ID not found in session'
        );
      }

      // 2. Get pagination and filter parameters from query
      const {
        page = 1,
        limit = 10,
        status,
        sortBy = 'submitted_at',
        sortOrder = 'desc'
      } = req.query;
      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);
      const offset = (pageNum - 1) * limitNum;

      // 3. Get all project IDs associated with this instructor using the efficient array operator
      const instructorProjects = await Project.findAll({
        where: {
          instructor_id: { [Op.contains]: [instructorId] }
        },
        attributes: ['id'],
        raw: true
      });

      // Since Project.findAll always returns an array, we can map it directly.
      const projectIds = instructorProjects.map(project => project.id);

      // 4. Handle the case where the instructor has no projects, exiting early.
      if (projectIds.length === 0) {
        return {
          submissions: [],
          pagination: {
            currentPage: pageNum,
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: limitNum
          },
          instructorInfo: { instructorId, totalProjects: 0 }
        };
      }

      // 5. Build the where clause for the submissions query
      const whereClause = { project_id: projectIds };
      if (status) {
        whereClause.status = status;
      }

      // 6. Get the paginated list of submissions and the total count
      const { count, rows: submissions } = await Submission.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'profile_picture']
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'due_date', 'difficulty_level'],
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              }
            ]
          },
          {
            model: Grade,
            as: 'grade',
            required: false, // Use LEFT JOIN to include submissions without grades
            include: [
              {
                model: User,
                as: 'evaluator',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ],
        limit: limitNum,
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true // Important for correct counts with JOINs
      });

      // 7. Calculate submission statistics by status
      const statusCounts = await Submission.findAll({
        where: { project_id: projectIds },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      // Use reduce for a clean way to transform the status counts array into an object
      const statusStats = statusCounts.reduce((stats, item) => {
        stats[item.status] = parseInt(item.count, 10);
        return stats;
      }, {});

      const transformedSubmissions = this.transformSubmissions(submissions);

      // 8. Return the final, comprehensive response object
      return {
        submissions: transformedSubmissions,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(count / limitNum),
          totalItems: count,
          itemsPerPage: limitNum
        },
        instructorInfo: {
          instructorId,
          totalProjects: projectIds.length,
          projectIds
        },
        statistics: {
          totalSubmissions: count,
          statusBreakdown: statusStats,
          ...statusStats
        }
      };
    } catch (error) {
      throw error;
    }
  }

  static async getSubmissionByProjectId(req) {
    try {
      const { projectId } = req.params;
      const {
        page = 1,
        limit = 10,
        status,
        sortBy = 'submitted_at',
        sortOrder = 'desc'
      } = req.query;

      if (!projectId) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');
      }

      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);
      const offset = (pageNum - 1) * limitNum;

      const whereClause = { project_id: projectId };
      if (status) {
        whereClause.status = status;
      }

      const { count, rows: submissions } = await Submission.findAndCountAll({
        where: whereClause,
        include: this.getSubmissionIncludes(),
        limit: limitNum,
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true
      });

      const statusCounts = await Submission.findAll({
        where: { project_id: projectId },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      const statistics = statusCounts.reduce((acc, item) => {
        acc[item.status] = parseInt(item.count, 10);
        return acc;
      }, {
        total_submission_count: count,
        graded_count: 0,
        grading_count: 0
      });

      statistics.graded_count = statistics.graded || 0;
      statistics.grading_count = statistics.grading || 0;

      delete statistics.graded;
      delete statistics.grading;


      const userIds = submissions.map(s => s.user_id);
      const latestCheckpoints = await sequelize.query(
        `
        WITH LatestCheckpoints AS (
          SELECT
            cp."user_id",
            cp."submitted_at",
            cp."status",
            c."title",
            ROW_NUMBER() OVER(PARTITION BY cp."user_id" ORDER BY cp."submitted_at" DESC) as rn
          FROM "checkpoint_progress" AS cp
          JOIN "checkpoints" AS c ON cp."checkpoint_id" = c."id"
          WHERE cp."project_id" = :projectId AND cp."user_id" IN (:userIds)
        )
        SELECT "user_id", "submitted_at", "status", "title"
        FROM LatestCheckpoints
        WHERE rn = 1;
        `,
        {
          replacements: { projectId, userIds },
          type: sequelize.QueryTypes.SELECT
        }
      );

      const latestCheckpointMap = new Map(
        latestCheckpoints.map(cp => [cp.user_id, cp])
      );

      const submissionsWithDetails = submissions.map(submission => {
        const latestCheckpoint = latestCheckpointMap.get(submission.user_id);
        return {
          ...this.transformSubmission(submission),
          latest_checkpoint_name: latestCheckpoint?.title || null,
          latest_checkpoint_submitted_at: latestCheckpoint?.submitted_at || null,
          latest_checkpoint_status: latestCheckpoint?.status || null
        };
      });

      const project = await Project.findByPk(projectId, {
        attributes: ['id', 'title', 'status', 'due_date', 'difficulty_level'],
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ]
          }
        ]
      });

      return {
        submissions: submissionsWithDetails,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(count / limitNum),
          totalItems: count,
          itemsPerPage: limitNum
        },
        project: {
          id: project.id,
          title: project.title,
          status: project.status,
          dueDate: project.due_date,
          difficultyLevel: project.difficulty_level,
          course: project.course
            ? {
                id: project.course.id,
                name: project.course.name,
                code: project.course.code
              }
            : null
        },
        statistics,
        filters: {
          status,
          sortBy,
          sortOrder
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get submission by User ID and Project ID
   */
  static async getSubmissionByUserIdAndProjectId(req) {
    try {
      const { userId, projectId } = req.params;

      // Validate required parameters
      if (!userId || !projectId) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'User ID and Project ID are required'
        );
      }

      // 1. Fetch all checkpoints for the project
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId },
        order: [['checkpoint_number', 'ASC']],
        raw: true
      });

      // 2. Fetch all checkpoint progress for the user
      const checkpointProgress = await CheckpointProgress.findAll({
        where: { user_id: userId, project_id: projectId },
        raw: true
      });

      // Create a map for quick progress lookup
      const progressMap = new Map(checkpointProgress.map(p => [p.checkpoint_id, p]));

      // 3. Combine checkpoints with their progress and generate presigned URLs for checkpoint files
      const combinedCheckpoints = await Promise.all(
        checkpoints.map(async (checkpoint) => {
          const progress = progressMap.get(checkpoint.id);
          
          let filesWithUrls = [];
          if (progress && progress.files_submitted?.length > 0) {
            filesWithUrls = await this.generatePresignedUrls(req, progress.files_submitted, 'getSubmissionByUserIdAndProjectId-checkpoints');
          }

          return {
            ...checkpoint,
            progress: progress ? {
              status: progress.status,
              submittedAt: progress.submitted_at,
              attempts: progress.attempts,
              studentNotes: progress.student_notes,
              timeSpentMinutes: progress.time_spent_minutes,
              filesSubmitted: filesWithUrls,
              feedback: progress.feedback,
              grade: progress.grade,
              reviewedAt: progress.reviewed_at,
              returnedAt: progress.returned_at
            } : {
              status: 'not_started',
              submittedAt: null,
              attempts: 0,
              filesSubmitted: []
            }
          };
        })
      );

      // 4. Find main submission record (project-level)
      const submission = await Submission.findOne({
        where: {
          user_id: userId,
          project_id: projectId
        },
        include: this.getSubmissionIncludes()
      });

      if (!submission) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'Submission not found for the specified user and project'
        );
      }

      // Role-based access control
      const isAdmin = req.userRoles?.includes('admin');
      const isOwner = submission.user_id === req.user.id;
      const isInstructor = req.userRoles?.includes('instructor');
      const hasAccess = isAdmin || isOwner || isInstructor;

      if (!hasAccess) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to view this submission'
        );
      }

      // For instructors, verify they have access to this project
      // if (isInstructor && !isAdmin && !isOwner) {
      //   const instructorProjects = await Project.findAll({
      //     where: {
      //       instructor_id: { [Op.contains]: [req.user.id] }
      //     },
      //     attributes: ['id'],
      //     raw: true
      //   });

      //   const instructorProjectIds = instructorProjects.map(p => p.id);

      //   if (!instructorProjectIds.includes(projectId)) {
      //     throw new ApiError(httpStatus.FORBIDDEN, 'Access to this project submission is denied');
      //   }
      // }

      // Generate presigned URLs for notebooks if they exist
      let notebookPresignedUrls = [];
      let filePresignedUrls = [];

      if (submission?.metadata?.notebooks?.length) {
        notebookPresignedUrls = await this.generatePresignedUrls(req, submission.metadata.notebooks, 'getSubmissionByUserIdAndProjectId');
      }
      if (submission?.metadata?.files?.length) {
        filePresignedUrls = await this.generatePresignedUrls(req, submission.metadata.files, 'getSubmissionByUserIdAndProjectId');
      }

      // 7. Construct the final response
      const projectDetails = submission ? this.transformSubmission(submission) : await this.getProjectDetails(projectId);

      return {
        submission: projectDetails,
        checkpoints: combinedCheckpoints,
        notebookPresignedUrls,
        filePresignedUrls
      };

    } catch (error) {
      if (error instanceof ApiError) throw error;
      LoggerError(
        req,
        'Failed to retrieve submission details',
        'getSubmissionByUserIdAndProjectId',
        httpStatus.INTERNAL_SERVER_ERROR,
        error.stack
      );
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to retrieve submission details'
      );
    }
  }

  /**
   * Helper to get project details when a submission doesn't exist.
   */
  static async getProjectDetails(projectId) {
    const project = await Project.findByPk(projectId, {
      include: [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
        }
      ]
    });

    if (!project) {
      return null; // Return null if project not found, let the caller handle it
    }

    return {
      id: null, // No submission ID
      status: 'not_started',
      submittedAt: null,
      project: {
        id: project.id,
        title: project.title,
        dueDate: project.due_date,
        difficultyLevel: project.difficulty_level,
        course: project.course ? {
          id: project.course.id,
          name: project.course.dataValues.name,
          code: project.course.dataValues.code
        } : null
      },
      user: null, // No submission user
      grade: null
    };
  }

  /**
   * Helper to generate presigned URLs for a list of files.
   */
  static async generatePresignedUrls(req, files, functionName) {
    try {
      return await Promise.all(
        (files || []).map(async (file) => {
          try {
            const url = await S3Service.generatePresignedDownloadUrl(file.key, 3600);
            return { ...file, presignedUrl: url };
          } catch (err) {
            LoggerError(
              req,
              `Error generating presigned URL for ${file.key}`,
              functionName,
              httpStatus.INTERNAL_SERVER_ERROR,
              err.stack
            );
            return { ...file, presignedUrl: null };
          }
        })
      );
    } catch (error) {
     throw error;
    }
  }

  /**
   * Performs robust validation to ensure a user can submit to a project.
   * Checks for project existence, published status, user enrollment, and due dates.
   * @param {string} projectId The ID of the project.
   * @param {string} userId The ID of the user.
   * @param {object} transaction The Sequelize transaction object.
   * @returns {Promise<object>} The validated project object.
   */
  static async validateProjectAccess(projectId, userId, transaction) {
    const project = await Project.findByPk(projectId, {
      attributes: ['id', 'status', 'due_date', 'course_id'],
      transaction
    });

    // if (!project) {
    //   throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
    // }
    // if (project.status !== 'published') {
    //   throw new ApiError(httpStatus.FORBIDDEN, 'This project is not currently published or accepting submissions.');
    // }
    // if (project.due_date && new Date() > new Date(project.due_date)) {
    //   throw new ApiError(httpStatus.FORBIDDEN, 'The submission deadline for this project has passed.');
    // }

    // const enrollment = await LtiContextEnrollment.findOne({
    //   where: {
    //     user_id: userId,
    //     context_id: project.course_id,
    //     enrollment_status: 'active'
    //   },
    //   transaction
    // });

    // if (!enrollment) {
    //   throw new ApiError(httpStatus.FORBIDDEN, 'You are not actively enrolled in the course for this project.');
    // }

    return project;
  }

  /**
   * Create or update submission by moving files from user's directory.
   */
  static async createOrUpdateSubmission(req) {
    const transaction = await sequelize.transaction();
    try {
      const {
        projectId,
        checkpointId,
        submissionSummary,
        timeSpentMinutes,
        filenamesToSubmit = [] // Array of filenames in user's directory to be moved
      } = req.body;
      const userId = req.user.id;
      const userName = req.user.jupiterUserName;

      if (!userId) {
        throw new ApiError(
          httpStatus.UNAUTHORIZED,
          'User not found. Could not process submission.'
        );
      }

      // 1. ===== INITIAL VALIDATIONS =====
      if (!projectId || !userId || !checkpointId) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project ID, Checkpoint ID, and User ID are required.'
        );
      }
      if (req.userRoles?.includes('instructor')) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Instructors cannot create submissions.'
        );
      }

      // await this.validateProjectAccess(projectId, userId, transaction);

      const currentCheckpoint = await Checkpoint.findOne({
        where: { id: checkpointId, project_id: projectId },
        transaction
      });
      if (!currentCheckpoint) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'Checkpoint not found in this project.'
        );
      }

      // 2. ===== SEQUENTIAL CHECKPOINT VALIDATION =====
      await this._validatePreviousCheckpointsComplete(
        userId,
        projectId,
        currentCheckpoint,
        transaction
      );

      // 3. ===== GET CHECKPOINT PROGRESS & CHECK ATTEMPTS =====
      const [checkpointProgress] = await CheckpointProgress.findOrCreate({
        where: {
          user_id: userId,
          project_id: projectId,
          checkpoint_id: checkpointId
        },
        defaults: {
          user_id: userId,
          project_id: projectId,
          checkpoint_id: checkpointId
        },
        transaction
      });

      const maxAttempts = currentCheckpoint.max_attempts;
      if (maxAttempts > 0 && checkpointProgress.attempts >= maxAttempts) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          `You have reached the maximum of ${maxAttempts} submission attempts.`
        );
      }

      // 4. ===== HANDLE FILE OPERATIONS (MOVE ONLY) =====
      let submittedFileRecords = [];
      const submissionIdForPath = checkpointProgress.id;

      if (Array.isArray(filenamesToSubmit) && filenamesToSubmit.length > 0) {
        const movePromises = filenamesToSubmit.map(async filename => {
          const sourceKey = S3Service.generateFilePath(
            'user-project-dataset',
            userName,
            null,
            projectId,
            filename
          );
          const destinationKey = S3Service.generateFilePath(
            'submission',
            userName,
            null,
            projectId,
            filename,
            submissionIdForPath,
            checkpointId
          );

          // Copy the file to the new location and then delete the original.
          await S3Service.copyFile(sourceKey, destinationKey);

          return {
            key: destinationKey,
            fileName: filename,
            url: `${S3Service.baseUrl}/${destinationKey}`
          };
        });
        // Await all file operations to complete and assign the results
        submittedFileRecords = await Promise.all(movePromises);
      }

      // The logic for handling direct uploads (req.files) has been removed.

      // 5. ===== UPDATE CHECKPOINT PROGRESS TABLE =====
      await checkpointProgress.update(
        {
          status: 'submitted',
          submitted_at: new Date(),
          attempts: sequelize.literal('attempts + 1'),
          student_notes: submissionSummary,
          time_spent_minutes:
            timeSpentMinutes || checkpointProgress.time_spent_minutes,
          files_submitted: submittedFileRecords,
          returned_at: null
        },
        { transaction }
      );

      // 6. ===== TRIGGER CASCADING PROJECT COMPLETION CHECKS =====
      await this._handleProjectCompletionChecks(userId, projectId, transaction);

      await transaction.commit();
      return checkpointProgress;
    } catch (error) {
      await transaction.rollback();
      // logger.error('Error during submission process:', { error: error.message, stack: error.stack });
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        `Error processing submission: ${error.message}`
      );
    }
  }

  /** @private */
  static async _validatePreviousCheckpointsComplete(
    userId,
    projectId,
    currentCheckpoint,
    transaction
  ) {
    const requiredCheckpoints = await Checkpoint.findAll({
      where: {
        project_id: projectId,
        is_required: true,
        checkpoint_number: { [Op.lt]: currentCheckpoint.checkpoint_number }
      },
      order: [['checkpoint_number', 'ASC']],
      transaction,
      raw: true
    });

    if (requiredCheckpoints.length === 0) return;

    const progressRecords = await CheckpointProgress.findAll({
      where: {
        checkpoint_id: { [Op.in]: requiredCheckpoints.map(c => c.id) },
        user_id: userId
      },
      transaction,
      raw: true
    });

    const incomplete = requiredCheckpoints.find(reqCp => {
      const progress = progressRecords.find(p => p.checkpoint_id === reqCp.id);
      return (
        !progress ||
        !['submitted', 'reviewed', 'completed'].includes(progress.status)
      );
    });

    if (incomplete) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        `You must complete the required checkpoint "${incomplete.title}" before proceeding.`
      );
    }
  }

  /** @private */
  static async _handleProjectCompletionChecks(userId, projectId, transaction) {
    const allRequiredCheckpoints = await Checkpoint.findAll({
      where: { project_id: projectId, is_required: true },
      attributes: ['id'],
      transaction
    });

    if (allRequiredCheckpoints.length === 0) {
      await this._updateMasterProgressRecords(userId, projectId, transaction);
      return;
    }

    const completedProgressCount = await CheckpointProgress.count({
      where: {
        user_id: userId,
        project_id: projectId,
        checkpoint_id: { [Op.in]: allRequiredCheckpoints.map(c => c.id) },
        status: { [Op.in]: ['submitted', 'reviewed', 'completed'] }
      },
      transaction
    });

    if (completedProgressCount >= allRequiredCheckpoints.length) {
      await this._updateMasterProgressRecords(userId, projectId, transaction);
    }
  }

  /** @private */
  static async _updateMasterProgressRecords(userId, projectId, transaction) {
    const [submission] = await Submission.findOrCreate({
      where: { user_id: userId, project_id: projectId },
      defaults: { user_id: userId, project_id: projectId },
      transaction
    });

    if (submission.status !== 'submitted') {
      await submission.update(
        { status: 'submitted', submitted_at: new Date() },
        { transaction }
      );
    }

    await StudentProjectProgress.update(
      { status: 'completed', completion_date: new Date() },
      { where: { student_id: userId, project_id: projectId }, transaction }
    );
  }
  //   /**
  //    * Submit final assignment
  //    */

  static async submitAssignment(req) {
    const { id } = req.params;

    const submission = await Submission.findByPk(id, {
      include: [
        {
          model: Project,
          as: 'project',
          include: [
            {
              model: LtiContext,
              as: 'course',
              attributes: [
                'id',
                ['context_title', 'name'],
                ['context_label', 'code']
              ],
              raw: true
            }
          ]
        }
      ]
    });

    this.validateSubmission(submission, req.user.id);

    const timeSpent = this.calculateTimeSpent(submission);
    const attempts = (submission.attempts || 0) + 1;
    const finalMetadata = {
      ...(submission.metadata || {}),
      ...this.buildFinalSubmissionMetadata(submission)
    };

    const updatedSubmission = await submission.update({
      status: 'submitted',
      submitted_at: new Date(),
      time_spent: timeSpent,
      attempts,
      metadata: finalMetadata
    });

    const result = {
      id: updatedSubmission.id,
      status: updatedSubmission.status,
      submittedAt: updatedSubmission.submitted_at,
      timeSpent: updatedSubmission.time_spent,
      attempts: updatedSubmission.attempts,
      projectTitle: submission.project?.title,
      courseName: submission.project?.course?.name,
      notebooks: updatedSubmission.metadata?.notebooks || [],
      files: updatedSubmission.metadata?.files || []
    };

    return result;
  }

  static async downloadSubmissionNotebook(req) {
    try {
      const { id } = req.params;

      const submission = await Submission.findByPk(id, {
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              }
            ]
          },
          { model: User, as: 'user', attributes: ['id', 'name', 'email'] }
        ]
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      const isAdmin = req.userRoles?.includes('admin');
      const isOwner = submission.user_id === req.user.id;
      const isInstructor =
        submission.project?.course?.instructor_id === req.user.id;
      const hasAccess = isAdmin || isOwner || isInstructor;

      if (!hasAccess) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to download this submission'
        );
      }

      const notebooks = submission.metadata?.notebooks || [];
      if (!notebooks.length) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'No notebook file(s) found for this submission'
        );
      }

      const selectedNotebook = notebooks[0];

      //Getting the download URL
      const presignedUrl = await S3Service.generatePresignedDownloadUrl(
        selectedNotebook.key,
        3600
      );

      //Sanitize filename for download
      const sanitizedName = submission.user.name.replace(/[^a-zA-Z0-9]/g, '_');
      const sanitizedTitle = submission.project.title.replace(
        /[^a-zA-Z0-9]/g,
        '_'
      );
      const fileName = `${sanitizedName}_${sanitizedTitle}_${submission.id}.ipynb`;

      return {
        downloadUrl: presignedUrl,
        fileName,
        expiresIn: 3600,
        submission: {
          id: submission.id,
          status: submission.status,
          projectTitle: submission.project.title,
          courseName: submission.project.course.name,
          studentName: submission.user.name,
          submittedAt: submission.submitted_at
        }
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;

      LoggerError(
        req,
        'Unexpected error in downloadSubmissionNotebook',
        'downloadSubmissionNotebook',
        httpStatus.INTERNAL_SERVER_ERROR,
        error
      );

      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to download submission notebook'
      );
    }
  }

  //   /**
  //    * Get submission statistics
  //    */
  static async getSubmissionStatistics(req) {
    const { projectId } = req.params;

    const project = await Project.findByPk(projectId, {
      include: [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
          raw: true
        }
      ]
    });

    if (!project) {
      throw new Error('Project not found');
    }

    const hasPermission =
      req.userRoles?.includes('admin') ||
      project.course.instructor_id === req.user.id;

    if (!hasPermission) {
      throw new Error(
        'You do not have permission to view submission statistics'
      );
    }

    return await this.calculateStatistics(projectId, project);
  }

  //     // Private helper methods
  static getSubmissionIncludes() {
    return [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'profile_picture']
      },
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'title', 'due_date', 'difficulty_level'],
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ]
      },
      {
        model: Grade,
        as: 'grade',
        include: [
          {
            model: User,
            as: 'evaluator',
            attributes: ['id', 'name', 'email']
          }
        ]
      }
    ];
  }
  //     // Inside SubmissionService class

  static transformSubmissions(submissions) {
    return submissions.map(submission => this.transformSubmission(submission));
  }

  static transformSubmission(submission) {
    if (!submission) return null;
    return {
      id: submission.id,
      status: submission.status,
      submittedAt: submission.submitted_at,
      executionTime: submission.execution_time,
      timeSpent: submission.time_spent,
      attempts: submission.attempts,
      submissionSummary: submission.submission_summary,
      // notebooks: submission.metadata?.notebooks || [],
      // files: submission.metadata?.files || [],
      metadata: submission.metadata,
      user: submission.user
        ? {
            id: submission.user.id,
            name: submission.user.name,
            email: submission.user.email,
            profilePicture: submission.user.profile_picture
          }
        : null,
      project: submission.project
        ? {
            id: submission.project.id,
            title: submission.project.title,
            dueDate: submission.project.due_date,
            difficultyLevel: submission.project.difficulty_level,
            course: submission.project.course
              ? {
                  id: submission.project.course.id,
                  name: submission.project.course.name,
                  code: submission.project.course.code
                }
              : null
          }
        : null,
      grade: submission.grade
        ? {
            id: submission.grade.id,
            totalScore: submission.grade.total_score,
            maxScore: submission.grade.max_score,
            percentage: submission.grade.percentage,
            letterGrade: submission.grade.letter_grade,
            feedback: submission.grade.feedback,
            // evaluator: submission.grade.evaluator ? {
            //     id: submission.grade.evaluator.id,
            //     name: submission.grade.evaluator.name,
            //     email: submission.grade.evaluator.email
            // } : null,
            gradedAt: submission.grade.graded_at
          }
        : null,
      createdAt: submission.created_at,
      updatedAt: submission.updated_at
    };
  }

  static calculateTimeSpent(submission) {
    const lastSaved = submission.metadata?.lastAutoSave
      ? new Date(submission.metadata.lastAutoSave)
      : null;
    const currentTimeSpent = lastSaved
      ? (Date.now() - lastSaved.getTime()) / 1000
      : 0;
    return Math.round(submission.time_spent + currentTimeSpent);
  }

  // static buildAutoSaveMetadata(submission, newMetadata) {
  //   return {
  //     ...submission.metadata,
  //     ...newMetadata,
  //     lastAutoSave: new Date(),
  //     autoSaveCount: (submission.metadata?.autoSaveCount || 0) + 1,
  //     lastModified: new Date()
  //   };
  // }

  // static validateSubmission(submission, userId) {
  //   if (!submission) throw new Error('Submission not found');
  //   if (submission.user_id !== userId) throw new Error('Access denied');
  //   if (submission.status === 'submitted')
  //     throw new Error('Assignment has already been submitted');
  //   if (!submission.notebook_s3_url)
  //     throw new Error('Please upload your notebook before submitting');
  //   if (
  //     submission.project.due_date &&
  //     new Date() > new Date(submission.project.due_date)
  //   ) {
  //     throw new Error('Submission deadline has passed');
  //   }
  // }

  static buildFinalSubmissionMetadata(submission) {
    return {
      ...(submission.metadata || {}),

      // ✅ preserve uploaded files and notebooks
      files: submission.metadata?.files || [],
      notebooks: submission.metadata?.notebooks || [],
      finalSubmission: true,
      submissionComplete: true,
      completedAt: new Date(),
      lastModified: new Date(),
      submissionHistory: [
        ...(submission.metadata?.submissionHistory || []),
        {
          timestamp: new Date(),
          status: 'submitted',
          timeSpent: submission.time_spent,
          attempts: submission.attempts
        }
      ]
    };
  }

  static async calculateStatistics(projectId, project) {
    const totalEnrolled = await LtiContextEnrollment.count({
      where: {
        context_id: project.course_id,
        role_in_course: 'student',
        enrollment_status: 'active'
      }
    });

    const totalSubmissions = await Submission.count({
      where: { project_id: projectId }
    });

    return {
      overview: {
        totalEnrolled,
        totalSubmissions,
        submissionRate:
          totalEnrolled > 0
            ? ((totalSubmissions / totalEnrolled) * 100).toFixed(1)
            : 0
      },
      metadata: {
        projectTitle: project.title,
        courseName: project.course.name,
        dueDate: project.due_date,
        generatedAt: new Date()
      }
    };
  }

  static async createInstructorReviewSandbox(req) {
    try {
      const { id: checkpointProgressId } = req.params;
      const instructor = req.user;

      // 1. FETCH & AUTHORIZE: Get the submission and verify access.
      const checkpointProgress =
        await CheckpointProgress.findByPk(checkpointProgressId);

      if (!checkpointProgress) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'Checkpoint progress not found'
        );
      }

      if (req.userRoles?.includes('student')) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Students cannot create review sandboxes.'
        );
      }

      // const hasAccess = this._canUserAccessCheckpoint(
      //   instructor,
      //   req.userRoles,
      //   checkpointProgress
      // );
      // if (!hasAccess) {
      //   throw new ApiError(
      //     httpStatus.FORBIDDEN,
      //     'You do not have permission to review this submission.'
      //   );
      // }

      const filesToSync = checkpointProgress.files_submitted || [];
      if (filesToSync.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'This submission has no files to review.'
        );
      }

      logger.info(`filesToSync: ${JSON.stringify(filesToSync, null, 2)}`);

      // 2. EXECUTE: Call the JupyterService with the required data.
      const sandboxPath = `reviews/${checkpointProgressId}`;
      const sandboxDetails = await jupyterService.createSandboxForReview(
        instructor,
        sandboxPath,
        filesToSync
      );
      logger.info(`filesToSync -- sandboxPath: ${sandboxPath}`);

      // 3. RESPOND: Build the final URL and return the complete response.
      const sandboxUrl = `${config.jupyterhub.url}/user/${encodeURIComponent(
        instructor.jupiterUserName
      )}/lab/tree/${encodeURIComponent(sandboxPath)}?token=${encodeURIComponent(instructor.jupyterUserToken)}`;

      return {
        message: 'Review sandbox created successfully.',
        sandboxUrl,
        ...sandboxDetails
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      LoggerError(
        req,
        'Failed to create instructor review sandbox',
        'createInstructorReviewSandbox',
        httpStatus.INTERNAL_SERVER_ERROR,
        error
      );
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to create review sandbox.'
      );
    }
  }
}

export default SubmissionService;
