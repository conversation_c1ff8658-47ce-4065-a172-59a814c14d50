import {
  Grade,
  Submission,
  User,
  Project,
  LtiContext,
  Rubric,
  Checkpoint,
  CheckpointProgress,
  StudentProjectProgress,
  LtiContextEnrollment
} from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';
import { LoggerInfo } from '../utils/helpers.utils.js';

/**
 * Enhanced Grades Service
 * Provides comprehensive grade data including project-wise and checkpoint information
 */
class EnhancedGradesService {
  /**
   * Get enhanced grade data for a student
   * @param {string} studentId - Student user ID
   * @param {Object} options - Query options
   * @returns {Object} Enhanced grade data
   */
  async getEnhancedStudentGrades(studentId, options = {}) {
    const {
      page = 1,
      limit = 10,
      projectId,
      courseId,
      includeAnalytics = true,
      includeCheckpoints = true
    } = options;

    try {
      // Get base grades with enhanced project data
      const grades = await this.getGradesWithProjectData(studentId, {
        page,
        limit,
        projectId,
        courseId
      });

      // Enhance each grade with additional data
      const enhancedGrades = await Promise.all(
        grades.rows.map(async grade => {
          try {
            const enhancedGrade = await this.enhanceGradeData(grade, {
              includeAnalytics,
              includeCheckpoints
            });
            return enhancedGrade;
          } catch (error) {
            logger.error('Error enhancing individual grade:', error);
            return this.getBasicGradeData(grade);
          }
        })
      );

      return {
        grades: enhancedGrades,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(grades.count / parseInt(limit)),
          totalItems: grades.count,
          itemsPerPage: parseInt(limit)
        }
      };
    } catch (error) {
      logger.error('Error getting enhanced student grades:', error);
      throw error;
    }
  }

  /**
   * Get grades with enhanced project data
   * @param {string} studentId - Student user ID
   * @param {Object} options - Query options
   * @returns {Object} Grades with project data
   */
  async getGradesWithProjectData(studentId, options = {}) {
    const { page, limit, projectId, courseId } = options;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const whereClause = {};
    const includeClause = [
      {
        model: Submission,
        as: 'submission',
        where: { user_id: studentId },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'profile_picture']
          },
          {
            model: Project,
            as: 'project',
            attributes: [
              'id',
              'title',
              'description',
              'due_date',
              'due_date',
              'difficulty_level',
              'estimated_hours',
              'total_points',
              'status',
              'is_team_project',
              'max_team_size',
              'learning_objectives',
              'prerequisites'
            ],
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                required: false
              },
              {
                model: Rubric,
                as: 'rubrics',
                attributes: [
                  'id',
                  'title',
                  'description',
                  'criteria',
                  'total_points'
                ],
                required: false
              }
            ]
          }
        ]
      },
      {
        model: User,
        as: 'evaluator',
        attributes: ['id', 'name', 'email']
      }
    ];

    // Apply filters
    if (projectId) {
      includeClause[0].include[1].where = { id: projectId };
    }

    if (courseId) {
      includeClause[0].include[1].include[0].where = { id: courseId };
    }

    let count = 0;
    let rows = [];

    try {
      const result = await Grade.findAndCountAll({
        where: whereClause,
        include: includeClause,
        limit: parseInt(limit),
        offset,
        order: [['graded_at', 'DESC']],
        distinct: true
      });
      count = result.count;
      rows = result.rows.map(grade => grade.get({ plain: true }));
      LoggerInfo(
        '',
        'fetch the greades with project details successfully',
        'getGradesWithProjectData'
      );
    } catch (error) {
      logger.error('Database query error, falling back to basic query:', error);
      // Fallback to simpler query without complex includes
      const basicResult = await Grade.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Submission,
            as: 'submission',
            where: { user_id: studentId },
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email']
              },
              {
                model: Project,
                as: 'project',
                attributes: ['id', 'title', 'due_date']
              }
            ]
          }
        ],
        limit: parseInt(limit),
        offset,
        order: [['graded_at', 'DESC']]
      });
      count = basicResult.count;
      rows = basicResult.rows;
    }

    return { count, rows };
  }

  /**
   * Enhance individual grade data with project progress and checkpoint information
   * @param {Object} grade - Grade object from database
   * @param {Object} options - Enhancement options
   * @returns {Object} Enhanced grade data
   */
  async enhanceGradeData(grade, options = {}) {
    const {
      includeAnalytics = false,
      includeCheckpoints = true,
      includeRubricScores = false
    } = options;

    try {
      const projectId = grade.submission.project.id;
      const studentId = grade.submission.user.id;

      // Get project progress data
      const projectProgress = await this.getProjectProgressData(
        projectId,
        studentId
      );

      // Get checkpoint data if requested
      let checkpointData = null;
      if (includeCheckpoints) {
        checkpointData = await this.getCheckpointData(projectId, studentId);
      }

      // Get analytics if requested
      let analytics = undefined;
      if (includeAnalytics) {
        try {
          analytics = await this.getGradeAnalytics(grade, projectId);
        } catch (error) {
          logger.error('Error getting grade analytics:', error);
          analytics = {
            classAverage: 0,
            minGrade: 0,
            maxGrade: 0,
            totalStudents: 0,
            percentile: 0,
            gradeDistribution: {},
            trend: 'stable'
          };
        }
      }

      let rubricScores = undefined;
      if (includeRubricScores && grade.rubric_scores) {
        rubricScores = await this.transformRubricScoresToCheckpoints(
          grade.rubric_scores,
          projectId,
          studentId
        );
      }

      // Transform the grade data
      const enhancedGrade = {
        id: grade.id,
        totalScore: grade.total_score,
        maxScore: grade.max_score,
        percentage: grade.percentage,
        // letterGrade: grade.letter_grade,
        overAllFeedback: grade.feedback,
        rubricScores: rubricScores,
        gradedAt: grade.graded_at,
        submission: {
          id: grade.submission.id,
          submittedAt: grade.submission.submitted_at,
          status: grade.submission.status,
          user: {
            id: grade.submission.user.id,
            name: grade.submission.user.name,
            email: grade.submission.user.email,
            profilePicture: grade.submission.user.profile_picture
          },
          project: {
            id: grade.submission.project.id,
            title: grade.submission.project.title,
            description: grade.submission.project.description,
            dueDate: grade.submission.project.due_date,
            startDate: grade.submission.project.start_date,
            endDate: grade.submission.project.end_date,
            difficultyLevel: grade.submission.project.difficulty_level,
            estimatedHours: grade.submission.project.estimated_hours,
            totalPoints: grade.submission.project.total_points || 100,
            status: grade.submission.project.status,
            isTeamProject: grade.submission.project.is_team_project || false,
            maxTeamSize: grade.submission.project.max_team_size || 1,
            learningObjectives:
              grade.submission.project.learning_objectives || '',
            prerequisites: grade.submission.project.prerequisites || '',
            course: grade.submission.project.course
              ? {
                  id:
                    grade.submission.project.course.dataValues?.id ||
                    grade.submission.project.course.id,
                  name:
                    grade.submission.project.course.dataValues?.name ||
                    grade.submission.project.course.name ||
                    grade.submission.project.course.context_title,
                  code:
                    grade.submission.project.course.dataValues?.code ||
                    grade.submission.project.course.code ||
                    grade.submission.project.course.context_label
                }
              : null,
            /* rubrics:
              grade.submission.project.rubrics?.map(rubric => ({
                id: rubric.id,
                name: rubric.name,
                description: rubric.description,
                criteria: rubric.criteria,
                maxScore: rubric.max_score,
                weight: rubric.weight
              })) || [], */
            // Enhanced project data
            progress: projectProgress,
            checkpoints: checkpointData
          }
        },
        evaluator: {
          id: grade.evaluator.id,
          name: grade.evaluator.name,
          email: grade.evaluator.email
        },
        analytics: analytics,
        createdAt: grade.created_at,
        updatedAt: grade.updated_at
      };

      return enhancedGrade;
    } catch (error) {
      logger.error('Error enhancing grade data:', error);
      // Return basic grade data if enhancement fails
      return this.getBasicGradeData(grade);
    }
  }

  /**
   * Get project progress data for a student
   * @param {string} projectId - Project ID
   * @param {string} studentId - Student ID
   * @returns {Object} Project progress data
   */
  async getProjectProgressData(projectId, studentId) {
    try {
      const progress = await StudentProjectProgress.findOne({
        where: {
          project_id: projectId,
          student_id: studentId
        },
        attributes: [
          'progress_percentage',
          'current_phase',
          'time_spent_hours',
          'status',
          'start_date',
          'completion_date',
          'last_activity'
        ]
      });

      if (!progress) {
        return {
          progressPercentage: 0,
          currentPhase: 'not_started',
          timeSpentHours: 0,
          status: 'not_started',
          startDate: null,
          completionDate: null,
          lastActivity: null
        };
      }

      return {
        progressPercentage: parseFloat(progress.progress_percentage) || 0,
        currentPhase: progress.current_phase || 'not_started',
        timeSpentHours: parseFloat(progress.time_spent_hours) || 0,
        status: progress.status || 'not_started',
        startDate: progress.start_date,
        completionDate: progress.completion_date,
        lastActivity: progress.last_activity
      };
    } catch (error) {
      logger.error('Error getting project progress data:', error);
      return {
        progressPercentage: 0,
        currentPhase: 'not_started',
        timeSpentHours: 0,
        status: 'not_started',
        startDate: null,
        completionDate: null,
        lastActivity: null
      };
    }
  }

  /**
   * Get checkpoint data for a project and student
   * @param {string} projectId - Project ID
   * @param {string} studentId - Student ID
   * @param {Object} rubricScores - Grade rubric scores
   * @returns {Object} Checkpoint data
   */
  async getCheckpointData(projectId, studentId) {
    try {
      // Get all checkpoints for the project
      const checkpoints = await Checkpoint.findAll({
        where: {
          project_id: projectId
        },
        attributes: [
          'id',
          'title',
          'description',
          'checkpoint_number',
          'due_date',
          'weight_percentage',
          'is_required',
          'status'
        ],
        order: [['checkpoint_number', 'ASC']]
      });

      // Get progress for each checkpoint
      const checkpointProgress = await CheckpointProgress.findAll({
        where: {
          project_id: projectId,
          user_id: studentId
        },
        attributes: [
          'checkpoint_id',
          'status',
          'started_at',
          'submitted_at',
          'completed_at',
          'instructor_feedback',
          'time_spent_minutes'
        ]
      });

      // Create a map of checkpoint progress
      const progressMap = {};
      checkpointProgress.forEach(progress => {
        progressMap[progress.checkpoint_id] = progress;
      });

      // Combine checkpoint data with progress
      const checkpointDetails = checkpoints.map(checkpoint => {
        const progress = progressMap[checkpoint.id];
        return {
          id: checkpoint.id,
          title: checkpoint.title,
          description: checkpoint.description,
          checkpointNumber: checkpoint.checkpoint_number,
          dueDate: checkpoint.due_date,
          weightPercentage: parseFloat(checkpoint.weight_percentage) || 0,
          isRequired: checkpoint.is_required,
          status: progress?.status || 'not_started',
          startedAt: progress?.started_at,
          submittedAt: progress?.submitted_at,
          completedAt: progress?.completed_at,
          instructorFeedback: progress?.instructor_feedback,
          timeSpentMinutes: progress?.time_spent_minutes || 0
        };
      });

      // Calculate summary statistics
      const total = checkpointDetails.length;
      const completed = checkpointDetails.filter(
        cp => cp.status === 'completed'
      ).length;
      const inProgress = checkpointDetails.filter(
        cp => cp.status === 'in_progress'
      ).length;
      const notStarted = checkpointDetails.filter(
        cp => cp.status === 'not_started'
      ).length;
      const overdue = checkpointDetails.filter(cp => {
        if (!cp.dueDate || cp.status === 'completed') return false;
        return new Date(cp.dueDate) < new Date() && cp.status !== 'completed';
      }).length;

      return {
        total,
        completed,
        inProgress,
        notStarted,
        overdue,
        details: checkpointDetails
      };
    } catch (error) {
      logger.error('Error getting checkpoint data:', error);
      return {
        total: 0,
        completed: 0,
        inProgress: 0,
        notStarted: 0,
        overdue: 0,
        details: []
      };
    }
  }

  /**
   * Get grade analytics for comparison and context
   * @param {Object} grade - Grade object
   * @param {string} projectId - Project ID
   * @returns {Object} Grade analytics
   */
  async getGradeAnalytics(grade, projectId) {
    try {
      // Get class average for this project
      const classStats = await Grade.findAll({
        include: [
          {
            model: Submission,
            as: 'submission',
            where: { project_id: projectId },
            attributes: []
          }
        ],
        attributes: [
          [
            Grade.sequelize.fn('AVG', Grade.sequelize.col('percentage')),
            'average'
          ],
          [Grade.sequelize.fn('MIN', Grade.sequelize.col('percentage')), 'min'],
          [Grade.sequelize.fn('MAX', Grade.sequelize.col('percentage')), 'max'],
          [
            Grade.sequelize.fn('COUNT', Grade.sequelize.col('Grade.id')),
            'count'
          ]
        ],
        raw: true
      });

      const stats = classStats[0];
      const classAverage = parseFloat(stats.average) || 0;
      const minGrade = parseFloat(stats.min) || 0;
      const maxGrade = parseFloat(stats.max) || 0;
      const totalStudents = parseInt(stats.count) || 0;

      // Calculate percentile
      const betterThanCount = await Grade.count({
        include: [
          {
            model: Submission,
            as: 'submission',
            where: { project_id: projectId },
            attributes: []
          }
        ],
        where: {
          percentage: { [Op.lt]: grade.percentage }
        }
      });

      const percentile =
        totalStudents > 0
          ? Math.round((betterThanCount / totalStudents) * 100)
          : 0;

      // Get grade distribution
      const gradeDistribution = await this.getGradeDistribution(projectId);

      return {
        classAverage: Math.round(classAverage * 100) / 100,
        minGrade: Math.round(minGrade * 100) / 100,
        maxGrade: Math.round(maxGrade * 100) / 100,
        totalStudents,
        percentile,
        gradeDistribution,
        trend: this.calculateTrend(grade.percentage, classAverage)
      };
    } catch (error) {
      logger.error('Error getting grade analytics:', error);
      return {
        classAverage: 0,
        minGrade: 0,
        maxGrade: 0,
        totalStudents: 0,
        percentile: 0,
        gradeDistribution: {},
        trend: 'stable'
      };
    }
  }

  /**
   * Get grade distribution for a project
   * @param {string} projectId - Project ID
   * @returns {Object} Grade distribution
   */
  async getGradeDistribution(projectId) {
    try {
      const distribution = await Grade.findAll({
        include: [
          {
            model: Submission,
            as: 'submission',
            where: { project_id: projectId },
            attributes: []
          }
        ],
        attributes: [
          [
            Grade.sequelize.fn('COUNT', Grade.sequelize.col('Grade.id')),
            'count'
          ],
          [
            Grade.sequelize.literal(`CASE
            WHEN percentage >= 90 THEN 'A'
            WHEN percentage >= 80 THEN 'B'
            WHEN percentage >= 70 THEN 'C'
            WHEN percentage >= 60 THEN 'D'
            ELSE 'F'
          END`),
            'grade_band'
          ]
        ],
        group: [
          Grade.sequelize.literal(`CASE
          WHEN percentage >= 90 THEN 'A'
          WHEN percentage >= 80 THEN 'B'
          WHEN percentage >= 70 THEN 'C'
          WHEN percentage >= 60 THEN 'D'
          ELSE 'F'
        END`)
        ],
        raw: true
      });

      const result = { A: 0, B: 0, C: 0, D: 0, F: 0 };
      distribution.forEach(item => {
        result[item.grade_band] = parseInt(item.count);
      });

      return result;
    } catch (error) {
      logger.error('Error getting grade distribution:', error);
      return { A: 0, B: 0, C: 0, D: 0, F: 0 };
    }
  }

  /**
   * Calculate grade trend
   * @param {number} studentGrade - Student's grade percentage
   * @param {number} classAverage - Class average
   * @returns {string} Trend indicator
   */
  calculateTrend(studentGrade, classAverage) {
    const difference = studentGrade - classAverage;
    if (difference > 10) return 'excellent';
    if (difference > 5) return 'above_average';
    if (difference > -5) return 'average';
    if (difference > -10) return 'below_average';
    return 'needs_improvement';
  }

  /**
   * Transform rubric scores object to checkpoints array format
   * @param {Object} rubricScores - Rubric scores object
   * @param {string} projectId - Project ID
   * @param {string} studentId - Student ID
   * @returns {Array} Formatted checkpoints array
   */
  async transformRubricScoresToCheckpoints(
    rubricScores,
    projectId,
    studentId,
    isStudent = false
  ) {
    try {
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId },
        include: [{ model: Rubric, as: 'rubrics', required: false }],
        order: [['checkpoint_number', 'ASC']]
      });

      const checkpointProgress = await CheckpointProgress.findAll({
        where: { project_id: projectId, user_id: studentId }
      });

      const progressMap = {};
      checkpointProgress.forEach(progress => {
        progressMap[progress.checkpoint_id] = progress;
      });

      return checkpoints.map(checkpoint => {
        const progress = progressMap[checkpoint.id];
        const checkpointRubricData = rubricScores[checkpoint.id];

        // Calculate checkpoint total and max scores
        let checkpointTotalScore = 0;
        let checkpointMaxScore = 0;

        let rubrics =
          checkpoint.rubrics?.map(rubric => {
            const rubricScore = checkpointRubricData?.rubrics?.find(
              r => r.rubricId === rubric.id
            );

            let rubricTotalScore = 0;
            const criteria = rubric.criteria.map(criterion => {
              const criterionScore = rubricScore?.criteriaScores?.find(
                cs => cs.criterionName === criterion.name
              );
              rubricTotalScore += criterionScore?.score || 0;
              return {
                name: criterion.name,
                description: criterion.description,
                points: criterion.points,
                currentScore: criterionScore?.score
              };
            });

            checkpointTotalScore += rubricTotalScore;
            checkpointMaxScore += parseInt(rubric.total_points) || 0;

            return {
              id: rubric.id,
              title: rubric.title,
              description: rubric.description,
              criteria,
              total_points: rubric.total_points
            };
          }) || [];

        if (isStudent) {
          rubrics = undefined;
        }

        return {
          id: checkpoint.id,
          title: checkpoint.title,
          description: checkpoint.description,
          checkpoint_number: checkpoint.checkpoint_number,
          start_date: checkpoint.due_date,
          due_date: checkpoint.due_date,
          weight_percentage: checkpoint.weight_percentage,
          is_required: checkpoint.is_required,
          status: checkpoint.status,
          isCompleted: progress?.status === 'completed',
          checkpointFeedback: checkpointRubricData?.checkpointFeedback || '',
          checkpointTotalScore: parseInt(checkpointTotalScore) || 0,
          checkpointMaxScore: parseInt(checkpointMaxScore) || 0,
          progress: progress
            ? {
                id: progress.id,
                status: progress.status,
                started_at: progress.started_at,
                submitted_at: progress.submitted_at,
                completed_at: progress.completed_at,
                time_spent_minutes: progress.time_spent_minutes || 0,
                last_activity: progress.last_activity
              }
            : {},
          rubrics
        };
      });
    } catch (error) {
      logger.error('Error transforming rubric scores to checkpoints:', error);
      return [];
    }
  }

  /**
   * Get checkpoint statistics for a project
   * @param {string} projectId - Project ID
   * @returns {Array} Checkpoint statistics
   */
  async getCheckpointStatistics(projectId) {
    try {
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId },
        order: [['checkpoint_number', 'ASC']]
      });

      const checkpointStats = await Promise.all(
        checkpoints.map(async checkpoint => {
          // Get total enrolled students for this project
          const project = await Project.findByPk(projectId, {
            include: [
              {
                model: LtiContext,
                as: 'course'
              }
            ]
          });

          const totalStudents = await User.count({
            include: [
              {
                model: LtiContextEnrollment,
                as: 'enrollments',
                where: {
                  context_id: project.course_id,
                  role_in_course: 'student'
                }
              }
            ]
          });

          // Get checkpoint progress stats
          const progressStats = await CheckpointProgress.findAll({
            where: {
              checkpoint_id: checkpoint.id,
              project_id: projectId
            },
            attributes: [
              'status',
              [
                CheckpointProgress.sequelize.fn(
                  'COUNT',
                  CheckpointProgress.sequelize.col('id')
                ),
                'count'
              ]
            ],
            group: ['status'],
            raw: true
          });

          // Get graded submissions for this checkpoint
          const gradedCount = await Grade.count({
            include: [
              {
                model: Submission,
                as: 'submission',
                where: { project_id: projectId }
              }
            ],
            where: {
              [`rubric_scores.${checkpoint.id}`]: { [Op.ne]: null }
            }
          });

          const statusCounts = {
            completed: 0,
            in_progress: 0,
            not_started: 0
          };

          progressStats.forEach(stat => {
            statusCounts[stat.status] = parseInt(stat.count) || 0;
          });

          const totalSubmissions =
            statusCounts.completed + statusCounts.in_progress;
          const notStartedCount = totalStudents - totalSubmissions;
          const totalSubmissionsCount = totalSubmissions + notStartedCount;

          return {
            checkpointId: checkpoint.id,
            checkpointNumber: checkpoint.checkpoint_number,
            title: checkpoint.title,
            dueDate: checkpoint.due_date,
            totalStudents,
            totalSubmissions: totalSubmissionsCount,
            submittedCount: statusCounts.completed,
            inProgressCount: statusCounts.in_progress,
            notStartedCount,
            gradedCount
            /* submissionRate: totalStudents > 0 ? Math.round((totalSubmissions / totalStudents) * 100) : 0,
            completionRate: totalStudents > 0 ? Math.round((statusCounts.completed / totalStudents) * 100) : 0,
            gradingRate: totalSubmissions > 0 ? Math.round((gradedCount / totalSubmissions) * 100) : 0 */
          };
        })
      );

      return checkpointStats;
    } catch (error) {
      logger.error('Error getting checkpoint statistics:', error);
      return [];
    }
  }

  /**
   * Get basic grade data (fallback)
   * @param {Object} grade - Grade object
   * @returns {Object} Basic grade data
   */
  getBasicGradeData(grade) {
    if (!grade) {
      return {
        id: null,
        totalScore: 0,
        maxScore: 0,
        percentage: 0,
        letterGrade: 'N/A',
        feedback: '',
        rubricScores: {},
        gradedAt: null,
        submission: null,
        evaluator: null,
        createdAt: null,
        updatedAt: null
      };
    }

    return {
      id: grade.id,
      totalScore: grade.total_score,
      maxScore: grade.max_score,
      percentage: grade.percentage,
      letterGrade: grade.letter_grade,
      feedback: grade.feedback,
      rubricScores: grade.rubric_scores,
      gradedAt: grade.graded_at,
      submission: grade.submission
        ? {
            id: grade.submission.id,
            submittedAt: grade.submission.submitted_at,
            status: grade.submission.status,
            user: grade.submission.user
              ? {
                  id: grade.submission.user.id,
                  name: grade.submission.user.name,
                  email: grade.submission.user.email,
                  profilePicture: grade.submission.user.profile_picture
                }
              : null,
            project: grade.submission.project
              ? {
                  id: grade.submission.project.id,
                  title: grade.submission.project.title,
                  dueDate: grade.submission.project.due_date,
                  course: grade.submission.project.course
                    ? {
                        id: grade.submission.project.course.id,
                        name: grade.submission.project.course.name,
                        code: grade.submission.project.course.code
                      }
                    : null
                }
              : null
          }
        : null,
      evaluator: grade.evaluator
        ? {
            id: grade.evaluator.id,
            name: grade.evaluator.name,
            email: grade.evaluator.email
          }
        : null,
      createdAt: grade.created_at,
      updatedAt: grade.updated_at
    };
  }
}

export default new EnhancedGradesService();
