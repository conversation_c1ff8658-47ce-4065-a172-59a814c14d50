import {
  Checkpoint,
  CheckpointGoal,
  CheckpointProgress,
  CheckpointGrade,
  Project,
  User
} from '../models/associations.js';
import logger from '../config/logger.config.js';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';
import {
  checkAccessRole,
  LoggerInfo,
  validateDates
} from '../utils/helpers.utils.js';
import enhancedProjectUtils from '../utils/enhancedProject.utils.js';

class CheckpointService {
  /**
   * Create a new checkpoint for a project
   */
  async createCheckpoint(req, transaction = null) {
    try {
      const {
        project_id,
        title,
        description,
        checkpoint_number,
        start_date,
        due_date,
        weight_percentage,
        is_required,
        status = 'draft',
        isScreen = 3,
        is_template = false,
        template_name
      } = req.body;

      if (!project_id || !title || !checkpoint_number)
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project ID, title, and checkpoint number are required'
        );

      const project = await enhancedProjectUtils.checkProjectExist(project_id);
      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      if (project.status === 'published')
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Published project related checkpoint cannot be created'
        );
      if (project.created_by !== req.user.id)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can create the Checkpoint'
        );

      if (start_date && due_date) {
        validateDates(start_date, due_date);
      }

      // Check if checkpoint with same project_id and checkpoint_number already exists
      const existingCheckpoint = await Checkpoint.findOne({
        where: {
          project_id,
          checkpoint_number: parseInt(checkpoint_number)
        },
        transaction
      });

      if (existingCheckpoint) {
        throw new ApiError(
          httpStatus.CONFLICT,
          `A checkpoint with checkpoint number ${checkpoint_number} already exists for this project`
        );
      }

      const checkpointData = {
        project_id,
        title,
        description,
        checkpoint_number: parseInt(checkpoint_number),
        due_date: due_date ? new Date(due_date) : null,
        start_date: start_date ? new Date(start_date) : null,
        weight_percentage: parseFloat(weight_percentage) || 0,
        is_required: is_required !== undefined ? is_required : true,
        status,
        created_by: req.user.id,
        is_template,
        template_name: is_template ? template_name : null
      };
      const checkpoint = await Checkpoint.create(checkpointData, {
        transaction
      });

      if (isScreen == 3) {
        await enhancedProjectUtils.updateProjectDetails(
          project_id,
          {
            isScreen: 3
          },
          transaction
        );
      }

      /* // If goals are provided, create them
      if (checkpointData.goals && Array.isArray(checkpointData.goals)) {
        const goals = checkpointData.goals.map(goal => ({
          ...goal,
          checkpoint_id: checkpoint.id
        }));

        await CheckpointGoal.bulkCreate(goals, { transaction });
      } */

      LoggerInfo(
        req,
        `Checkpoint created: ${checkpoint.id} for project ${checkpointData.project_id}`,
        'CheckpointService'
      );
      return checkpoint;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get checkpoints for a project with goals
   */
  async getProjectCheckpoints(projectId, includeGoals = true) {
    try {
      const includeClause = includeGoals
        ? [
            {
              model: CheckpointGoal,
              as: 'goals',
              order: [['order_index', 'ASC']]
            }
          ]
        : [];

      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId },
        include: includeClause,
        order: [['checkpoint_number', 'ASC']]
      });

      return checkpoints;
    } catch (error) {
      logger.error('Error fetching project checkpoints:', error);
      throw error;
    }
  }

  /**
   * Get checkpoint details with goals and progress
   */
  async getCheckpointDetails(checkpointId, userId = null) {
    try {
      const includeClause = [
        {
          model: CheckpointGoal,
          as: 'goals',
          order: [['order_index', 'ASC']]
        }
      ];

      if (userId) {
        includeClause.push({
          model: CheckpointProgress,
          as: 'progress',
          where: { user_id: userId },
          required: false,
          include: [
            {
              model: CheckpointGrade,
              as: 'grade'
            }
          ]
        });
      }

      const checkpoint = await Checkpoint.findByPk(checkpointId, {
        include: includeClause
      });

      return checkpoint;
    } catch (error) {
      logger.error('Error fetching checkpoint details:', error);
      throw error;
    }
  }

  /**
   * Update checkpoint progress for a student
   */
  async updateCheckpointProgress(checkpointId, userId, projectId, updates) {
    try {
      const [progress, created] = await CheckpointProgress.findOrCreate({
        where: {
          checkpoint_id: checkpointId,
          user_id: userId,
          project_id: projectId
        },
        defaults: {
          status: 'in_progress',
          started_at: new Date(),
          last_activity: new Date(),
          ...updates
        }
      });

      if (!created) {
        await progress.update({
          ...updates,
          last_activity: new Date()
        });
      }

      // Update project-level progress
      await this.updateProjectProgress(projectId, userId);

      logger.info(
        `Checkpoint progress updated: ${checkpointId} for user ${userId}`
      );
      return progress;
    } catch (error) {
      logger.error('Error updating checkpoint progress:', error);
      throw error;
    }
  }

  /**
   * Submit checkpoint for review
   */
  async submitCheckpoint(checkpointId, userId, projectId, submissionData) {
    try {
      const progress = await CheckpointProgress.findOne({
        where: {
          checkpoint_id: checkpointId,
          user_id: userId,
          project_id: projectId
        }
      });

      if (!progress) {
        throw new Error('Checkpoint progress not found');
      }

      await progress.update({
        status: 'submitted',
        submitted_at: new Date(),
        last_activity: new Date(),
        ...submissionData
      });

      logger.info(`Checkpoint submitted: ${checkpointId} by user ${userId}`);
      return progress;
    } catch (error) {
      logger.error('Error submitting checkpoint:', error);
      throw error;
    }
  }

  /**
   * Grade a checkpoint
   */
  async gradeCheckpoint(checkpointProgressId, evaluatorId, gradeData) {
    try {
      const progress = await CheckpointProgress.findByPk(checkpointProgressId, {
        include: [
          {
            model: Checkpoint,
            as: 'checkpoint'
          }
        ]
      });

      if (!progress) {
        throw new Error('Checkpoint progress not found');
      }

      // Calculate percentage
      const percentage = (gradeData.total_score / gradeData.max_score) * 100;

      // Create or update grade
      const [grade, created] = await CheckpointGrade.findOrCreate({
        where: { checkpoint_progress_id: checkpointProgressId },
        defaults: {
          evaluator_id: evaluatorId,
          ...gradeData,
          percentage
        }
      });

      if (!created) {
        await grade.update({
          ...gradeData,
          percentage
        });
      }

      // Update progress status
      await progress.update({
        status: 'completed',
        completed_at: new Date()
      });

      logger.info(
        `Checkpoint graded: ${checkpointProgressId} by evaluator ${evaluatorId}`
      );
      return grade;
    } catch (error) {
      logger.error('Error grading checkpoint:', error);
      throw error;
    }
  }

  /**
   * Calculate project progress for a student
   */
  async calculateProjectProgress(projectId, userId) {
    try {
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId, status: 'published' },
        include: [
          {
            model: CheckpointProgress,
            where: { user_id: userId },
            required: false,
            as: 'progress'
          }
        ]
      });

      let totalWeight = 0;
      let completedWeight = 0;
      let totalPoints = 0;
      let earnedPoints = 0;

      const checkpointDetails = checkpoints.map(checkpoint => {
        const progress = checkpoint.progress;
        const isCompleted = progress && progress.status === 'completed';

        totalWeight += parseFloat(checkpoint.weight_percentage);
        if (isCompleted) {
          completedWeight += parseFloat(checkpoint.weight_percentage);
        }

        // Calculate points if grade exists
        if (progress && progress.grade) {
          totalPoints += parseFloat(progress.grade.max_score);
          earnedPoints += parseFloat(progress.grade.total_score);
        }

        return {
          id: checkpoint.id,
          title: checkpoint.title,
          checkpoint_number: checkpoint.checkpoint_number,
          status: progress?.status || 'not_started',
          weight: checkpoint.weight_percentage,
          dueDate: checkpoint.due_date,
          progress: progress,
          isCompleted
        };
      });

      const progressPercentage =
        totalWeight > 0 ? (completedWeight / totalWeight) * 100 : 0;
      const gradePercentage =
        totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0;

      return {
        totalCheckpoints: checkpoints.length,
        completedCheckpoints: checkpoints.filter(
          cp => cp.progress && cp.progress.status === 'completed'
        ).length,
        progressPercentage,
        gradePercentage,
        totalWeight,
        completedWeight,
        totalPoints,
        earnedPoints,
        checkpoints: checkpointDetails
      };
    } catch (error) {
      logger.error('Error calculating project progress:', error);
      throw error;
    }
  }

  /**
   * Get instructor dashboard data for a course
   */
  async getInstructorDashboard(courseId, instructorId) {
    try {
      const projects = await Project.findAll({
        where: { course_id: courseId },
        include: [
          {
            model: Checkpoint,
            as: 'checkpoints',
            where: { status: 'published' },
            required: false,
            include: [
              {
                model: CheckpointProgress,
                as: 'progress',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['id', 'name', 'email']
                  }
                ]
              }
            ]
          }
        ]
      });

      const dashboardData = projects.map(project => {
        const totalStudents = project.checkpoints[0]?.progress?.length || 0;
        const checkpointStats = project.checkpoints.map(checkpoint => {
          const progress = checkpoint.progress || [];
          const completed = progress.filter(
            p => p.status === 'completed'
          ).length;
          const inProgress = progress.filter(
            p => p.status === 'in_progress'
          ).length;
          const submitted = progress.filter(
            p => p.status === 'submitted'
          ).length;

          return {
            id: checkpoint.id,
            title: checkpoint.title,
            totalStudents,
            completed,
            inProgress,
            submitted,
            notStarted: totalStudents - completed - inProgress - submitted
          };
        });

        return {
          id: project.id,
          title: project.title,
          totalCheckpoints: project.checkpoints.length,
          checkpointStats
        };
      });

      return dashboardData;
    } catch (error) {
      logger.error('Error fetching instructor dashboard:', error);
      throw error;
    }
  }

  /**
   * Get student progress overview for a course
   */
  async getStudentProgressOverview(courseId, projectId = null) {
    try {
      const whereClause = { course_id: courseId };
      if (projectId) {
        whereClause.id = projectId;
      }

      const projects = await Project.findAll({
        where: whereClause,
        include: [
          {
            model: Checkpoint,
            as: 'checkpoints',
            where: { status: 'published' },
            required: false,
            include: [
              {
                model: CheckpointProgress,
                as: 'progress',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['id', 'name', 'email']
                  }
                ]
              }
            ]
          }
        ]
      });

      return projects.map(project => {
        const totalStudents = project.checkpoints[0]?.progress?.length || 0;
        const overallProgress = project.checkpoints.reduce(
          (acc, checkpoint) => {
            const progress = checkpoint.progress || [];
            const completed = progress.filter(
              p => p.status === 'completed'
            ).length;
            acc.total += totalStudents;
            acc.completed += completed;
            return acc;
          },
          { total: 0, completed: 0 }
        );

        return {
          id: project.id,
          title: project.title,
          totalStudents,
          overallProgress:
            overallProgress.total > 0
              ? (overallProgress.completed / overallProgress.total) * 100
              : 0,
          checkpoints: project.checkpoints.map(checkpoint => ({
            id: checkpoint.id,
            title: checkpoint.title,
            totalStudents,
            completed: (checkpoint.progress || []).filter(
              p => p.status === 'completed'
            ).length,
            inProgress: (checkpoint.progress || []).filter(
              p => p.status === 'in_progress'
            ).length,
            submitted: (checkpoint.progress || []).filter(
              p => p.status === 'submitted'
            ).length
          }))
        };
      });
    } catch (error) {
      logger.error('Error fetching student progress overview:', error);
      throw error;
    }
  }

  /**
   * Bulk update checkpoints for a project
   */
  async bulkUpdateCheckpoints(req) {
    const transaction = await Checkpoint.sequelize.transaction();

    try {
      const {
        project_id,
        checkpoints,
        is_template = false,
        template_name
      } = req.body;

      if (!Array.isArray(checkpoints)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Checkpoints must be an array'
        );
      }

      // Verify project exists and user has permission
      const project = await enhancedProjectUtils.checkProjectExist(project_id);
      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      if (project.status === 'published') {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Published project checkpoints cannot be modified'
        );
      }

      if (project.created_by !== req.user.id) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can update checkpoints'
        );
      }

      // Get existing checkpoints for this project
      const existingCheckpoints = await Checkpoint.findAll({
        where: { project_id },
        transaction
      });

      const existingIds = existingCheckpoints.map(cp => cp.id);
      const incomingIds = checkpoints
        .filter(cp => cp.id && cp.id !== '')
        .map(cp => cp.id);
      const idsToDelete = existingIds.filter(id => !incomingIds.includes(id));

      const results = {
        created: [],
        updated: [],
        deleted: []
      };

      // Step 1: Delete first to free up checkpoint_numbers
      if (idsToDelete.length > 0) {
        const { Rubric } = await import('../models/associations.js');
        await Rubric.destroy({
          where: { checkpoint_id: idsToDelete },
          transaction
        });
        await Checkpoint.destroy({ where: { id: idsToDelete }, transaction });
        results.deleted = idsToDelete;
      }

      // Step 2: Process updates and creates
      for (const checkpointData of checkpoints) {
        const {
          id,
          title,
          description,
          checkpoint_number,
          start_date,
          due_date,
          weight_percentage,
          is_required,
          status = 'draft'
        } = checkpointData;

        if (!title || checkpoint_number === undefined) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Title and checkpoint number are required'
          );
        }

        // For creates, check if checkpoint_number already exists
        if (!id || id === '') {
          const existingCheckpoint = await Checkpoint.findOne({
            where: {
              project_id,
              checkpoint_number: parseInt(checkpoint_number)
            },
            transaction
          });

          if (existingCheckpoint) {
            throw new ApiError(
              httpStatus.CONFLICT,
              `A checkpoint with checkpoint number ${checkpoint_number} already exists for this project`
            );
          }
        }

        if (start_date && due_date) {
          validateDates(start_date, due_date);
        }

        const processedData = {
          project_id,
          title,
          description,
          checkpoint_number: parseInt(checkpoint_number),
          start_date: checkpointData.start_date
            ? new Date(checkpointData.start_date)
            : null,
          due_date: checkpointData.due_date
            ? new Date(checkpointData.due_date)
            : null,
          weight_percentage: parseFloat(weight_percentage) || 0,
          is_required: is_required !== undefined ? is_required : true,
          status,
          created_by: req.user.id,
          is_template,
          template_name
        };

        let checkpoint;

        if (id && id !== '') {
          // Update existing checkpoint
          checkpoint = await Checkpoint.findByPk(id, { transaction });
          if (!checkpoint) {
            throw new ApiError(
              httpStatus.NOT_FOUND,
              `Checkpoint with id ${id} not found`
            );
          }

          await checkpoint.update(processedData, { transaction });
          results.updated.push(checkpoint.id);
        } else {
          // Create new checkpoint
          checkpoint = await Checkpoint.create(processedData, { transaction });
          results.created.push(checkpoint.id);
        }
      }

      await transaction.commit();

      LoggerInfo(
        req,
        `Bulk checkpoint update for project ${project_id}: Created ${results.created.length}, Updated ${results.updated.length}, Deleted ${results.deleted.length}`,
        'bulkUpdateCheckpoints'
      );

      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Delete checkpoint and associated rubrics
   */
  async deleteCheckpoint(req) {
    const transaction = await Checkpoint.sequelize.transaction();

    try {
      const { id } = req.params;

      const checkpoint = await Checkpoint.findByPk(id, {
        include: [
          {
            model: Project,
            as: 'project'
          }
        ],
        transaction
      });

      if (!checkpoint) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Checkpoint not found');
      }

      // Check permissions
      if (checkpoint.project && checkpoint.project.created_by !== req.user.id) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can delete the checkpoint'
        );
      }

      if (checkpoint.project && checkpoint.project.status === 'published') {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Cannot delete checkpoint from published project'
        );
      }

      // Delete associated rubrics first
      const { Rubric } = await import('../models/associations.js');
      await Rubric.destroy({ where: { checkpoint_id: id }, transaction });

      // Delete checkpoint goals
      // await CheckpointGoal.destroy({ where: { checkpoint_id: id }, transaction });

      // Delete checkpoint
      await checkpoint.destroy({ transaction });

      await transaction.commit();

      LoggerInfo(
        req,
        `Checkpoint deleted: ${checkpoint.title} by user ${req.user.id}`,
        'deleteCheckpoint'
      );

      return {
        message: 'Checkpoint and associated rubrics deleted successfully'
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update project progress when checkpoint progress changes
   */
  async updateProjectProgress(projectId, userId) {
    try {
      // This method can be extended to update project-level metrics
      // For now, it's a placeholder for future enhancements
      logger.debug(
        `Project progress updated for project ${projectId}, user ${userId}`
      );
    } catch (error) {
      logger.error('Error updating project progress:', error);
      // Don't throw error as this is not critical
    }
  }
}

export default new CheckpointService();
