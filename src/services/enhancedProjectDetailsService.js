import { 
  Project, 
  Course, 
  User, 
  StudentProjectProgress, 
  Checkpoint, 
  CheckpointProgress,
  ProjectAssignment,
  Submission,
  Grade,
  CourseEnrollment
} from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.js';

class EnhancedProjectDetailsService {
  
  /**
   * Get comprehensive project details for students
   */
  async getEnhancedProjectDetails(projectId, userId, userRole) {
    try {
      const project = await this.getProjectWithRelations(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Get all enhanced data
      const [
        studentProgress,
        checkpoints,
        timeline,
        teamInfo,
        staffInfo,
        sandboxInfo,
        submissions,
        notebookManagement
      ] = await Promise.all([
        this.getStudentProgress(projectId, userId),
        this.getProjectCheckpointsWithProgress(projectId, userId),
        this.calculateProjectTimeline(project, userId),
        this.getProjectTeamInfo(projectId, userId),
        this.getProjectStaffInfo(projectId),
        this.getProjectSandboxInfo(projectId, userId),
        this.********************************(projectId, userId),
        this.getProjectNotebookManagement(projectId, userId)
      ]);

      return {
        studentProgress,
        checkpoints,
        timeline,
        team: teamInfo,
        staff: staffInfo,
        sandbox: sandboxInfo,
        submissions,
        notebookManagement
      };
    } catch (error) {
      logger.error('Error getting enhanced project details:', error);
      throw error;
    }
  }

  /**
   * Get project with all necessary relations
   */
  async getProjectWithRelations(projectId) {
    return await Project.findByPk(projectId, {
      include: [
        {
          model: Course,
          as: 'course',
          include: [{
            model: User,
            as: 'instructor',
            attributes: ['id', 'name', 'email']
          }]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }
      ]
    });
  }

  /**
   * Get student progress for the project
   */
  async getStudentProgress(projectId, userId) {
    try {
      const progress = await StudentProjectProgress.findOne({
        where: { 
          project_id: projectId, 
          student_id: userId 
        }
      });

      if (!progress) {
        return {
          progressPercentage: 0,
          currentPhase: null,
          timeSpentHours: 0,
          lastActivity: null,
          status: 'not_started',
          enrollmentDate: null,
          startDate: null,
          completionDate: null,
          grade: null,
          feedback: null
        };
      }

      return {
        progressPercentage: parseFloat(progress.progress_percentage) || 0,
        currentPhase: progress.current_phase,
        timeSpentHours: parseFloat(progress.time_spent_hours) || 0,
        lastActivity: progress.last_activity,
        status: progress.status,
        enrollmentDate: progress.enrollment_date,
        startDate: progress.start_date,
        completionDate: progress.completion_date,
        grade: progress.grade ? parseFloat(progress.grade) : null,
        feedback: progress.feedback
      };
    } catch (error) {
      logger.error('Error getting student progress:', error);
      return null;
    }
  }

  /**
   * Get project checkpoints with student progress
   */
  async getProjectCheckpointsWithProgress(projectId, userId) {
    try {
      const checkpoints = await Checkpoint.findAll({
        where: { 
          project_id: projectId, 
          status: 'published' 
        },
        order: [['checkpoint_number', 'ASC']],
        include: [
          {
            model: CheckpointProgress,
            as: 'studentProgress',
            where: { student_id: userId },
            required: false
          }
        ]
      });

      const checkpointDetails = checkpoints.map(checkpoint => {
        const studentProgress = checkpoint.studentProgress?.[0];
        
        return {
          id: checkpoint.id,
          title: checkpoint.title,
          description: checkpoint.description,
          checkpointNumber: checkpoint.checkpoint_number,
          dueDate: checkpoint.due_date,
          weightPercentage: parseFloat(checkpoint.weight_percentage) || 0,
          isRequired: checkpoint.is_required,
          status: studentProgress ? studentProgress.status : 'not_started',
          submittedAt: studentProgress?.submitted_at || null,
          grade: studentProgress?.grade ? parseFloat(studentProgress.grade) : null,
          feedback: studentProgress?.feedback || null,
          goals: checkpoint.goals || []
        };
      });

      // Calculate checkpoint summary
      const total = checkpoints.length;
      const completed = checkpointDetails.filter(cp => cp.status === 'completed').length;
      const inProgress = checkpointDetails.filter(cp => cp.status === 'in_progress').length;
      const notStarted = checkpointDetails.filter(cp => cp.status === 'not_started').length;
      const overdue = checkpointDetails.filter(cp => {
        if (!cp.dueDate || cp.status === 'completed') return false;
        return new Date(cp.dueDate) < new Date();
      }).length;

      return {
        total,
        completed,
        inProgress,
        notStarted,
        overdue,
        details: checkpointDetails
      };
    } catch (error) {
      logger.error('Error getting project checkpoints:', error);
      return {
        total: 0,
        completed: 0,
        inProgress: 0,
        notStarted: 0,
        overdue: 0,
        details: []
      };
    }
  }

  /**
   * Calculate enhanced project timeline
   */
  async calculateProjectTimeline(project, userId) {
    try {
      const now = new Date();
      const startDate = project.start_date || project.created_at;
      const endDate = project.end_date || project.due_date;
      const dueDate = project.due_date;

      // Calculate duration in days
      const duration = endDate ? 
        Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) : 
        null;

      // Calculate days remaining
      const daysRemaining = dueDate ? 
        Math.ceil((new Date(dueDate) - now) / (1000 * 60 * 60 * 24)) : 
        null;

      // Check if overdue
      const isOverdue = dueDate ? new Date(dueDate) < now : false;

      // Get upcoming deadlines (checkpoints)
      const upcomingDeadlines = await this.getUpcomingDeadlines(project.id, userId);

      // Determine progress status
      let progressStatus = 'on_track';
      if (isOverdue) {
        progressStatus = 'overdue';
      } else if (daysRemaining && daysRemaining < 7) {
        progressStatus = 'urgent';
      } else if (daysRemaining && daysRemaining < 14) {
        progressStatus = 'approaching';
      }

      return {
        duration,
        daysRemaining,
        isOverdue,
        progressStatus,
        startDate,
        endDate,
        dueDate,
        upcomingDeadlines
      };
    } catch (error) {
      logger.error('Error calculating project timeline:', error);
      return {
        duration: null,
        daysRemaining: null,
        isOverdue: false,
        progressStatus: 'on_track',
        startDate: null,
        endDate: null,
        dueDate: null,
        upcomingDeadlines: []
      };
    }
  }

  /**
   * Get upcoming deadlines for the project
   */
  async getUpcomingDeadlines(projectId, userId) {
    try {
      const checkpoints = await Checkpoint.findAll({
        where: { 
          project_id: projectId, 
          status: 'published',
          due_date: {
            [Op.gte]: new Date()
          }
        },
        order: [['due_date', 'ASC']],
        limit: 5
      });

      return checkpoints.map(checkpoint => {
        const daysRemaining = Math.ceil((new Date(checkpoint.due_date) - new Date()) / (1000 * 60 * 60 * 24));
        
        return {
          type: 'checkpoint',
          id: checkpoint.id,
          title: checkpoint.title,
          dueDate: checkpoint.due_date,
          daysRemaining,
          status: daysRemaining <= 3 ? 'urgent' : daysRemaining <= 7 ? 'upcoming' : 'future',
          weightPercentage: parseFloat(checkpoint.weight_percentage) || 0
        };
      });
    } catch (error) {
      logger.error('Error getting upcoming deadlines:', error);
      return [];
    }
  }

  /**
   * Get project team information
   */
  async getProjectTeamInfo(projectId, userId) {
    try {
      // For now, return basic team info
      // This would be enhanced with actual team management
      return {
        isTeamProject: false,
        maxTeamSize: 1,
        currentTeamSize: 1,
        members: [
          {
            id: userId,
            name: 'Current User',
            email: '<EMAIL>',
            role: 'member',
            joinedAt: new Date()
          }
        ],
        invitations: []
      };
    } catch (error) {
      logger.error('Error getting project team info:', error);
      return {
        isTeamProject: false,
        maxTeamSize: 1,
        currentTeamSize: 1,
        members: [],
        invitations: []
      };
    }
  }

  /**
   * Get project staff information
   */
  async getProjectStaffInfo(projectId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: { 
          project_id: projectId,
          is_active: true
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      const instructors = [];
      const teachingAssistants = [];
      const reviewers = [];

      assignments.forEach(assignment => {
        const staffMember = {
          id: assignment.user.id,
          name: assignment.user.name,
          email: assignment.user.email,
          role: assignment.role,
          officeHours: assignment.metadata?.officeHours || null
        };

        switch (assignment.role) {
          case 'instructor':
            instructors.push(staffMember);
            break;
          case 'ta':
            teachingAssistants.push(staffMember);
            break;
          case 'reviewer':
            reviewers.push(staffMember);
            break;
        }
      });

      return {
        instructors,
        teachingAssistants,
        reviewers
      };
    } catch (error) {
      logger.error('Error getting project staff info:', error);
      return {
        instructors: [],
        teachingAssistants: [],
        reviewers: []
      };
    }
  }

  /**
   * Get project sandbox information
   */
  async getProjectSandboxInfo(projectId, userId) {
    try {
      // For now, return basic sandbox info
      // This would be enhanced with actual sandbox management
      return {
        isAvailable: true,
        status: 'active',
        createdAt: new Date(),
        lastAccessed: new Date(),
        settings: {
          environment: 'jupyter',
          resources: {
            cpu: '2 cores',
            memory: '4GB',
            storage: '10GB'
          }
        },
        notebooks: [
          {
            id: 'notebook-1',
            name: 'main_notebook.ipynb',
            status: 'active',
            lastModified: new Date(),
            size: '2.5MB'
          }
        ]
      };
    } catch (error) {
      logger.error('Error getting project sandbox info:', error);
      return {
        isAvailable: false,
        status: 'inactive',
        createdAt: null,
        lastAccessed: null,
        settings: null,
        notebooks: []
      };
    }
  }

  /**
   * Get project submissions with detailed grading
   */
  async ********************************(projectId, userId) {
    try {
      const submissions = await Submission.findAll({
        where: { 
          project_id: projectId, 
          user_id: userId 
        },
        include: [
          {
            model: Grade,
            as: 'grade',
            include: [{
              model: User,
              as: 'evaluator',
              attributes: ['id', 'name', 'email']
            }]
          }
        ],
        order: [['submitted_at', 'DESC']]
      });

      if (submissions.length === 0) {
        return {
          total: 0,
          latest: null,
          history: []
        };
      }

      const latest = submissions[0];
      const history = submissions.slice(1);

      return {
        total: submissions.length,
        latest: {
          id: latest.id,
          status: latest.status,
          submittedAt: latest.submitted_at,
          grade: latest.grade ? {
            totalScore: latest.grade.total_score,
            maxScore: latest.grade.max_score,
            percentage: latest.grade.percentage,
            letterGrade: latest.grade.letter_grade,
            feedback: latest.grade.feedback,
            evaluator: latest.grade.evaluator,
            gradedAt: latest.grade.created_at
          } : null
        },
        history: history.map(submission => ({
          id: submission.id,
          status: submission.status,
          submittedAt: submission.submitted_at,
          grade: submission.grade ? {
            totalScore: submission.grade.total_score,
            percentage: submission.grade.percentage,
            letterGrade: submission.grade.letter_grade
          } : null
        }))
      };
    } catch (error) {
      logger.error('Error getting project submissions:', error);
      return {
        total: 0,
        latest: null,
        history: []
      };
    }
  }

  /**
   * Get project notebook management information
   */
  async getProjectNotebookManagement(projectId, userId) {
    try {
      const project = await Project.findByPk(projectId, {
        attributes: [
          'notebook_template_s3_url',
          'dataset_s3_url',
          'additional_files_s3_urls'
        ]
      });

      if (!project) {
        return {
          template: null,
          dataset: null,
          additionalFiles: [],
          studentNotebooks: []
        };
      }

      return {
        template: project.notebook_template_s3_url ? {
          url: project.notebook_template_s3_url,
          name: 'Project Template',
          size: '1.2MB' // This would be calculated from S3
        } : null,
        dataset: project.dataset_s3_url ? {
          url: project.dataset_s3_url,
          name: 'Project Dataset',
          size: '5.8MB', // This would be calculated from S3
          description: 'Main dataset for the project'
        } : null,
        additionalFiles: project.additional_files_s3_urls || [],
        studentNotebooks: [
          {
            id: 'student-notebook-1',
            name: 'my_work.ipynb',
            status: 'active',
            lastModified: new Date(),
            size: '3.2MB',
            version: 5
          }
        ]
      };
    } catch (error) {
      logger.error('Error getting project notebook management:', error);
      return {
        template: null,
        dataset: null,
        additionalFiles: [],
        studentNotebooks: []
      };
    }
  }

  /**
   * Launch sandbox for a project
   */
  async launchSandbox(projectId, userId) {
    try {
      // This would integrate with actual sandbox orchestrator
      logger.info(`Launching sandbox for project ${projectId}, user ${userId}`);
      
      return {
        success: true,
        sandboxId: `sandbox-${projectId}-${userId}`,
        status: 'launching',
        estimatedReadyTime: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
      };
    } catch (error) {
      logger.error('Error launching sandbox:', error);
      throw error;
    }
  }

  /**
   * Get project statistics for students
   */
  async getProjectStatistics(projectId, userId) {
    try {
      const [
        progress,
        checkpoints,
        submissions,
        timeline
      ] = await Promise.all([
        this.getStudentProgress(projectId, userId),
        this.getProjectCheckpointsWithProgress(projectId, userId),
        this.********************************(projectId, userId),
        this.calculateProjectTimeline(await this.getProjectWithRelations(projectId), userId)
      ]);

      return {
        progress: progress.progressPercentage,
        checkpointsCompleted: checkpoints.completed,
        checkpointsTotal: checkpoints.total,
        submissionsCount: submissions.total,
        daysRemaining: timeline.daysRemaining,
        status: progress.status,
        isOverdue: timeline.isOverdue
      };
    } catch (error) {
      logger.error('Error getting project statistics:', error);
      throw error;
    }
  }
}

export default new EnhancedProjectDetailsService();
