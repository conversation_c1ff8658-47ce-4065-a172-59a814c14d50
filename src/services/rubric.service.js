import { Project, User, Rubric, Checkpoint } from '../models/associations.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.config.js';
import logger from '../config/logger.config.js';
import enhancedProjectUtils from '../utils/enhancedProject.utils.js';
import httpStatus from 'http-status';
import ApiError from '../utils/ApiError.utils.js';
import { LoggerInfo } from '../utils/helpers.utils.js';

class rubricService {
  /**
   * Create one or more rubrics based on array or single object input
   */
  async creationOfRubric(req) {
    const transaction = await sequelize.transaction();
    try {
      const rubricsInput = Array.isArray(req.body) ? req.body : [req.body];
      const createdRubrics = [];

      for (const rubricData of rubricsInput) {
        const {
          project_id,
          checkpoint_id,
          title,
          description,
          criteria,
          total_points = 100,
          grading_scale,
          is_template = false,
          template_name
        } = rubricData;

        const created_by = req.user.id;

        // Validate that the project exists if not a template
        if (!project_id)
          throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');

        if (!checkpoint_id)
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Checkpoint ID is required'
          );

        // Validate checkpoint exists and belongs to the project
        const checkpoint = await Checkpoint.findOne({
          where: {
            id: checkpoint_id,
            project_id: project_id
          },
          transaction
        });

        if (!checkpoint)
          throw new ApiError(
            httpStatus.NOT_FOUND,
            'Checkpoint not found or does not belong to this project'
          );

        if (project_id) {
          const project =
            await enhancedProjectUtils.checkProjectExist(project_id);
          if (!project)
            throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
          if (project.status === 'published')
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Published project related rubric cannot be created'
            );
          if (project.created_by !== req.user.id)
            throw new ApiError(
              httpStatus.FORBIDDEN,
              'Only project creator can create the Rubric'
            );
        }

        // Validate criteria structure
        if (!Array.isArray(criteria) || criteria.length === 0)
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Criteria must be a non-empty array'
          );

        // Validate each criterion
        for (const criterion of criteria) {
          if (
            !criterion.name ||
            !criterion.description ||
            criterion.points === undefined ||
            criterion.points === null ||
            criterion.points === ''
          )
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Each criterion must have name, description, and points'
            );
          if (criterion.points <= 0)
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Criterion points must be positive'
            );
        }

        // Calculate total points from criteria
        const calculatedTotalPoints = criteria.reduce(
          (sum, criterion) => sum + criterion.points,
          0
        );

        // Use calculated total if not provided or if it differs
        const finalTotalPoints = total_points || calculatedTotalPoints;

        if (Math.abs(calculatedTotalPoints - finalTotalPoints) > 0.01) {
          logger.warn(
            `Total points mismatch: provided ${total_points}, calculated ${calculatedTotalPoints}`
          );
        }

        // Create the rubric
        const rubric = await Rubric.create(
          {
            project_id: project_id,
            checkpoint_id,
            title,
            description,
            criteria,
            total_points: finalTotalPoints,
            grading_scale: grading_scale || {
              A: { min: 90, max: 100 },
              B: { min: 80, max: 89.99 },
              C: { min: 70, max: 79.99 },
              D: { min: 60, max: 69.99 },
              F: { min: 0, max: 59.99 }
            },
            is_template,
            template_name: is_template ? template_name : null,
            created_by
          },
          { transaction }
        );

        // Fetch the created rubric with related data
        const createdRubric = await Rubric.findByPk(rubric.id, {
          include: [
            {
              model: Project,
              as: 'project',
              attributes: ['id', 'title', 'description', 'status']
            },
            {
              model: Checkpoint,
              as: 'checkpoint',
              attributes: ['id', 'title', 'description']
            }
          ],
          attributes: [
            'id',
            'title',
            'description',
            'criteria',
            'total_points'
          ],
          transaction
        });

        LoggerInfo(
          req,
          `Rubric created: ${title} by user ${created_by} (${is_template ? 'template' : 'project-specific'})`,
          'creationOfRubric'
        );

        createdRubrics.push(createdRubric);
      }

      await transaction.commit();
      // Return array if input was array, else single object
      return Array.isArray(req.body) ? createdRubrics : createdRubrics[0];
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get rubrics with filtering, pagination, and sorting
   */
  async getRubrics(req) {
    try {
      const {
        project_id,
        checkpoint_id,
        created_by,
        is_template,
        template_name,
        page = 1,
        limit = 20,
        sort_by = 'created_at',
        sort_order = 'DESC'
      } = req.query;

      // Build where clause
      const whereClause = {};
      if (project_id) whereClause.project_id = project_id;
      if (checkpoint_id) whereClause.checkpoint_id = checkpoint_id;
      if (created_by) whereClause.created_by = created_by;
      if (is_template !== undefined)
        whereClause.is_template = is_template === 'true';
      if (template_name)
        whereClause.template_name = {
          [Op.iLike]: `%${template_name}%`
        };

      // Pagination
      const offset = (page - 1) * limit;

      // Get rubrics with pagination
      const { count, rows: rubrics } = await Rubric.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status']
          },
          {
            model: Checkpoint,
            as: 'checkpoint',
            attributes: ['id', 'title', 'description', 'due_date']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [[sort_by, sort_order.toUpperCase()]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      if (rubrics.length === 0) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'No rubrics found matching the criteria'
        );
      }

      return {
        rubrics,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * fetch rubric by ID
   */
  async getRubricById(req) {
    try {
      const { id } = req.params;

      const rubric = await Rubric.findByPk(id, {
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status', 'course_id']
          },
          {
            model: Checkpoint,
            as: 'checkpoint',
            attributes: ['id', 'title', 'description', 'due_date']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!rubric) throw new ApiError(httpStatus.NOT_FOUND, 'Rubric not found');

      return rubric;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update a rubric by ID
   */
  async updateRubric(req) {
    const transaction = await sequelize.transaction();
    try {
      const { id } = req.params;
      const {
        title,
        description,
        criteria,
        total_points,
        grading_scale,
        is_template,
        template_name,
        checkpoint_mapping,
        metadata,
        project_id,
        checkpoint_id
      } = req.body;

      // Find existing rubric
      const rubric = await Rubric.findByPk(id, { transaction });
      if (!rubric) throw new ApiError(httpStatus.NOT_FOUND, 'Rubric not found');

      // Use existing values if not provided
      const finalProjectId = project_id || rubric.project_id;
      const finalCheckpointId = checkpoint_id || rubric.checkpoint_id;

      if (!finalProjectId)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');

      if (!finalCheckpointId)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Checkpoint ID is required');

      // Validate checkpoint exists and belongs to project
      const checkpoint = await Checkpoint.findOne({
        where: {
          id: finalCheckpointId,
          project_id: finalProjectId
        },
        transaction
      });

      if (!checkpoint)
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'Checkpoint not found or does not belong to this project'
        );

      const project =
        await enhancedProjectUtils.checkProjectExist(finalProjectId);
      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      if (project.status === 'published')
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Published project rubric cannot be updated'
        );

      if (project.created_by !== req.user.id)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can update the rubric'
        );

      // Validate criteria if provided
      if (criteria) {
        if (!Array.isArray(criteria) || criteria.length === 0)
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Criteria must be a non-empty array'
          );

        for (const criterion of criteria) {
          if (
            !criterion.name ||
            !criterion.description ||
            criterion.points === undefined
          )
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Each criterion must have name, description, and points'
            );
          if (criterion.points <= 0)
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Criterion points must be positive'
            );
        }
      }

      // Update the rubric
      const updateData = {};
      if (title !== undefined) updateData.title = title;
      if (description !== undefined) updateData.description = description;
      if (criteria !== undefined) updateData.criteria = criteria;
      if (total_points !== undefined) updateData.total_points = total_points;
      if (grading_scale !== undefined) updateData.grading_scale = grading_scale;
      if (is_template !== undefined) updateData.is_template = is_template;
      if (template_name !== undefined) updateData.template_name = template_name;
      if (checkpoint_mapping !== undefined)
        updateData.checkpoint_mapping = checkpoint_mapping;
      if (metadata !== undefined) updateData.metadata = metadata;
      if (finalCheckpointId !== rubric.checkpoint_id)
        updateData.checkpoint_id = finalCheckpointId;

      await rubric.update(updateData, { transaction });

      await transaction.commit();

      // Fetch updated rubric with relations
      const updatedRubric = await Rubric.findByPk(id, {
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status']
          },
          {
            model: Checkpoint,
            as: 'checkpoint',
            attributes: ['id', 'title', 'description']
          }
        ]
      });

      LoggerInfo(
        req,
        `Rubric updated: ${updatedRubric.title} by user ${req.user.id}`,
        'updateRubric'
      );

      return updatedRubric;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Bulk update rubrics for a project (create, update, delete)
   */
  async bulkUpdateRubrics(req) {
    const transaction = await sequelize.transaction();
    try {
      const {
        project_id,
        rubrics,
        is_template = false,
        template_name
      } = req.body;

      if (!project_id)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');

      if (!Array.isArray(rubrics))
        throw new ApiError(httpStatus.BAD_REQUEST, 'Rubrics must be an array');

      // Validate project exists and permissions
      const project = await enhancedProjectUtils.checkProjectExist(project_id);
      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      if (project.status === 'published')
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Published project rubrics cannot be updated'
        );

      if (project.created_by !== req.user.id)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can update rubrics'
        );

      // Get existing rubrics for this project
      const existingRubrics = await Rubric.findAll({
        where: { project_id },
        transaction
      });

      const existingRubricIds = existingRubrics.map(r => r.id);
      const incomingRubricIds = rubrics.filter(r => r.id).map(r => r.id);

      // Find rubrics to delete (exist in DB but not in incoming data)
      const rubricsToDelete = existingRubricIds.filter(
        id => !incomingRubricIds.includes(id)
      );

      const results = {
        created: [],
        updated: [],
        deleted: []
      };

      // Delete removed rubrics
      if (rubricsToDelete.length > 0) {
        await Rubric.destroy({
          where: { id: rubricsToDelete },
          transaction
        });
        results.deleted = rubricsToDelete;
      }

      // Process each rubric (create or update)
      for (const rubricData of rubrics) {
        const {
          id,
          title,
          description,
          criteria,
          total_points = 100,
          grading_scale,
          checkpoint_id,
          metadata = {}
        } = rubricData;

        if (!checkpoint_id)
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Checkpoint ID is required'
          );

        // Validate checkpoint exists and belongs to project
        const checkpoint = await Checkpoint.findOne({
          where: {
            id: checkpoint_id,
            project_id: project_id
          },
          transaction
        });

        if (!checkpoint)
          throw new ApiError(
            httpStatus.NOT_FOUND,
            'Checkpoint not found or does not belong to this project'
          );

        // Validate criteria
        if (!Array.isArray(criteria) || criteria.length === 0)
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Criteria must be a non-empty array'
          );

        for (const criterion of criteria) {
          if (!criterion.name || !criterion.description || !criterion.points)
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Each criterion must have name, description, and points'
            );
          if (criterion.points <= 0)
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Criterion points must be positive'
            );
        }

        const calculatedTotalPoints = criteria.reduce(
          (sum, criterion) => sum + criterion.points,
          0
        );
        const finalTotalPoints = total_points || calculatedTotalPoints;

        const rubricPayload = {
          project_id,
          checkpoint_id,
          title,
          description,
          criteria,
          total_points: finalTotalPoints,
          grading_scale: grading_scale || {
            A: { min: 90, max: 100 },
            B: { min: 80, max: 89.99 },
            C: { min: 70, max: 79.99 },
            D: { min: 60, max: 69.99 },
            F: { min: 0, max: 59.99 }
          },
          is_template,
          template_name: is_template ? template_name : null,
          created_by: req.user.id,
          metadata
        };

        if (id) {
          // Update existing rubric
          const existingRubric = await Rubric.findByPk(id, { transaction });
          if (!existingRubric)
            throw new ApiError(
              httpStatus.NOT_FOUND,
              `Rubric with id ${id} not found`
            );

          await existingRubric.update(rubricPayload, { transaction });
          results.updated.push(existingRubric.id);
        } else {
          // Create new rubric
          const newRubric = await Rubric.create(rubricPayload, { transaction });
          results.created.push(newRubric.id);
        }
      }

      await transaction.commit();

      LoggerInfo(
        req,
        `Bulk rubric update for project ${project_id}: Created ${results.created.length}, Updated ${results.updated.length}, Deleted ${results.deleted.length}`,
        'bulkUpdateRubrics'
      );

      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Delete a rubric by ID
   */
  async deleteRubric(req) {
    const transaction = await sequelize.transaction();
    try {
      const { id } = req.params;

      const rubric = await Rubric.findByPk(id, {
        include: [
          {
            model: Project,
            as: 'project'
          }
        ],
        transaction
      });

      if (!rubric) throw new ApiError(httpStatus.NOT_FOUND, 'Rubric not found');

      // Check permissions
      if (rubric.project && rubric.project.created_by !== req.user.id)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can delete the rubric'
        );

      if (rubric.project && rubric.project.status === 'published')
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Cannot delete rubric from published project'
        );

      await rubric.destroy({ transaction });
      await transaction.commit();

      LoggerInfo(
        req,
        `Rubric deleted: ${rubric.title} by user ${req.user.id}`,
        'deleteRubric'
      );

      return { message: 'Rubric deleted successfully' };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get rubric templates
   */
  async getRubricTemplates(req) {
    try {
      const { template_name, page = 1, limit = 20 } = req.query;

      const whereClause = { is_template: true };
      if (template_name)
        whereClause.template_name = {
          [Op.iLike]: `%${template_name}%`
        };

      const offset = (page - 1) * limit;

      const { count, rows: templates } = await Rubric.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        templates,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Duplicate a rubric template for a project
   */
  async duplicateRubricTemplate(req) {
    const transaction = await sequelize.transaction();
    try {
      const { id } = req.params;
      const { project_id, checkpoint_id, title, description } = req.body;

      // Find the template rubric
      const templateRubric = await Rubric.findByPk(id, { transaction });
      if (!templateRubric)
        throw new ApiError(httpStatus.NOT_FOUND, 'Rubric template not found');

      if (!templateRubric.is_template)
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Can only duplicate rubric templates'
        );

      if (!project_id)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');

      if (!checkpoint_id)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Checkpoint ID is required');

      // Validate project exists
      const project = await enhancedProjectUtils.checkProjectExist(project_id);
      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      if (project.created_by !== req.user.id)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Only project creator can duplicate rubrics'
        );

      // Validate checkpoint exists and belongs to the project
      const checkpoint = await Checkpoint.findOne({
        where: {
          id: checkpoint_id,
          project_id: project_id
        },
        transaction
      });

      if (!checkpoint)
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'Checkpoint not found or does not belong to this project'
        );

      // Create new rubric from template
      const duplicatedRubric = await Rubric.create(
        {
          project_id,
          checkpoint_id,
          title: title || `${templateRubric.title} (Copy)`,
          description: description || templateRubric.description,
          criteria: templateRubric.criteria,
          total_points: templateRubric.total_points,
          grading_scale: templateRubric.grading_scale,
          is_template: false,
          template_name: null,
          created_by: req.user.id,
          metadata: templateRubric.metadata || {}
        },
        { transaction }
      );

      await transaction.commit();

      // Fetch with relations
      const result = await Rubric.findByPk(duplicatedRubric.id, {
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status']
          },
          {
            model: Checkpoint,
            as: 'checkpoint',
            attributes: ['id', 'title', 'description']
          }
        ]
      });

      LoggerInfo(
        req,
        `Rubric template duplicated: ${result.title} for project ${project_id}`,
        'duplicateRubricTemplate'
      );

      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get rubrics for a specific project
   */
  async getProjectRubrics(req) {
    try {
      const { projectId } = req.params;

      // Validate project exists
      const project = await enhancedProjectUtils.checkProjectExist(projectId);
      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      const rubrics = await Rubric.findAll({
        where: { project_id: projectId },
        include: [
          {
            model: Checkpoint,
            as: 'checkpoint',
            attributes: ['id', 'title', 'description', 'due_date']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['created_at', 'DESC']]
      });

      return {
        project: {
          id: project.id,
          title: project.title,
          description: project.description,
          status: project.status
        },
        rubrics
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get rubric statistics
   */
  async getRubricStats(req) {
    try {
      const { created_by, is_template } = req.query;

      const whereClause = {};
      if (created_by) whereClause.created_by = created_by;
      if (is_template !== undefined)
        whereClause.is_template = is_template === 'true';

      const totalRubrics = await Rubric.count({ where: whereClause });
      const templateRubrics = await Rubric.count({
        where: { ...whereClause, is_template: true }
      });
      const projectRubrics = await Rubric.count({
        where: { ...whereClause, is_template: false }
      });

      return {
        total_rubrics: totalRubrics,
        template_rubrics: templateRubrics,
        project_rubrics: projectRubrics
      };
    } catch (error) {
      throw error;
    }
  }
}

export default new rubricService();
