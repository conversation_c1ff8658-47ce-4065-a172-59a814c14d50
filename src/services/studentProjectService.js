import { StudentProjectProgress, Checkpoint, CheckpointProgress, Project, Course, User } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.js';

class StudentProjectService {
  
  /**
   * Get enhanced project data for students
   */
  async getStudentProjectData(project, studentId) {
    try {
      // Get student progress for this project
      const studentProgress = await this.getStudentProgress(project.id, studentId);
      
      // Get checkpoint data with student progress
      const checkpointData = await this.getProjectCheckpoints(project.id, studentId);
      
      // Calculate timeline information
      const timelineData = this.calculateProjectTimeline(project, studentProgress);
      
      return {
        studentProgress,
        checkpoints: checkpointData,
        timeline: timelineData
      };
    } catch (error) {
      logger.error('Error getting student project data:', error);
      throw error;
    }
  }

  /**
   * Get student progress for a specific project
   */
  async getStudentProgress(projectId, studentId) {
    try {
      const progress = await StudentProjectProgress.findOne({
        where: { 
          project_id: projectId, 
          student_id: studentId 
        }
      });

      if (!progress) {
        // Return default progress if not found
        return {
          progressPercentage: 0,
          currentPhase: null,
          timeSpentHours: 0,
          lastActivity: null,
          status: 'not_started',
          enrollmentDate: null,
          startDate: null,
          completionDate: null,
          grade: null,
          feedback: null
        };
      }

      return {
        progressPercentage: parseFloat(progress.progress_percentage) || 0,
        currentPhase: progress.current_phase,
        timeSpentHours: parseFloat(progress.time_spent_hours) || 0,
        lastActivity: progress.last_activity,
        status: progress.status,
        enrollmentDate: progress.enrollment_date,
        startDate: progress.start_date,
        completionDate: progress.completion_date,
        grade: progress.grade ? parseFloat(progress.grade) : null,
        feedback: progress.feedback
      };
    } catch (error) {
      logger.error('Error getting student progress:', error);
      throw error;
    }
  }

  /**
   * Get project checkpoints with student progress
   */
  async getProjectCheckpoints(projectId, studentId) {
    try {
      const checkpoints = await Checkpoint.findAll({
        where: { 
          project_id: projectId, 
          status: 'published' 
        },
        order: [['checkpoint_number', 'ASC']],
        include: [
          {
            model: CheckpointProgress,
            as: 'studentProgress',
            where: { student_id: studentId },
            required: false
          }
        ]
      });

      const checkpointDetails = checkpoints.map(checkpoint => {
        const studentProgress = checkpoint.studentProgress?.[0];
        
        return {
          id: checkpoint.id,
          title: checkpoint.title,
          description: checkpoint.description,
          checkpointNumber: checkpoint.checkpoint_number,
          dueDate: checkpoint.due_date,
          weightPercentage: parseFloat(checkpoint.weight_percentage) || 0,
          isRequired: checkpoint.is_required,
          status: studentProgress ? studentProgress.status : 'not_started',
          submittedAt: studentProgress?.submitted_at || null,
          grade: studentProgress?.grade ? parseFloat(studentProgress.grade) : null,
          feedback: studentProgress?.feedback || null
        };
      });

      // Calculate checkpoint summary
      const total = checkpoints.length;
      const completed = checkpointDetails.filter(cp => cp.status === 'completed').length;
      const inProgress = checkpointDetails.filter(cp => cp.status === 'in_progress').length;
      const notStarted = checkpointDetails.filter(cp => cp.status === 'not_started').length;
      const overdue = checkpointDetails.filter(cp => {
        if (!cp.dueDate || cp.status === 'completed') return false;
        return new Date(cp.dueDate) < new Date();
      }).length;

      return {
        total,
        completed,
        inProgress,
        notStarted,
        overdue,
        details: checkpointDetails
      };
    } catch (error) {
      logger.error('Error getting project checkpoints:', error);
      throw error;
    }
  }

  /**
   * Calculate project timeline information
   */
  calculateProjectTimeline(project, studentProgress) {
    try {
      const now = new Date();
      const startDate = project.start_date || project.created_at;
      const endDate = project.end_date || project.due_date;
      const dueDate = project.due_date;

      // Calculate duration in days
      const duration = endDate ? 
        Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) : 
        null;

      // Calculate days remaining
      const daysRemaining = dueDate ? 
        Math.ceil((new Date(dueDate) - now) / (1000 * 60 * 60 * 24)) : 
        null;

      // Check if overdue
      const isOverdue = dueDate ? new Date(dueDate) < now : false;

      // Determine progress status
      let progressStatus = 'on_track';
      if (isOverdue && studentProgress.status !== 'completed') {
        progressStatus = 'overdue';
      } else if (daysRemaining && daysRemaining < 7 && studentProgress.progressPercentage < 80) {
        progressStatus = 'behind';
      } else if (studentProgress.progressPercentage > 90 && daysRemaining && daysRemaining > 14) {
        progressStatus = 'ahead';
      }

      return {
        duration,
        daysRemaining,
        isOverdue,
        progressStatus,
        startDate,
        endDate,
        dueDate
      };
    } catch (error) {
      logger.error('Error calculating project timeline:', error);
      return {
        duration: null,
        daysRemaining: null,
        isOverdue: false,
        progressStatus: 'on_track',
        startDate: null,
        endDate: null,
        dueDate: null
      };
    }
  }

  /**
   * Get multiple projects with student data (for listing)
   */
  async getStudentProjectsData(projects, studentId) {
    try {
      const enhancedProjects = await Promise.all(
        projects.map(async (project) => {
          const studentData = await this.getStudentProjectData(project, studentId);
          
          return {
            ...project,
            studentProgress: studentData.studentProgress,
            checkpoints: studentData.checkpoints,
            timeline: studentData.timeline
          };
        })
      );

      return enhancedProjects;
    } catch (error) {
      logger.error('Error getting student projects data:', error);
      throw error;
    }
  }

  /**
   * Initialize student progress for a project (if not exists)
   */
  async initializeStudentProgress(projectId, studentId, courseId) {
    try {
      const existingProgress = await StudentProjectProgress.findOne({
        where: { 
          project_id: projectId, 
          student_id: studentId 
        }
      });

      if (!existingProgress) {
        const progress = await StudentProjectProgress.create({
          student_id: studentId,
          project_id: projectId,
          course_id: courseId,
          enrollment_date: new Date(),
          progress_percentage: 0,
          time_spent_hours: 0,
          status: 'not_started'
        });

        logger.info(`Initialized student progress for project ${projectId}, student ${studentId}`);
        return progress;
      }

      return existingProgress;
    } catch (error) {
      logger.error('Error initializing student progress:', error);
      throw error;
    }
  }

  /**
   * Update student progress
   */
  async updateStudentProgress(projectId, studentId, progressData) {
    try {
      const progress = await StudentProjectProgress.findOne({
        where: { 
          project_id: projectId, 
          student_id: studentId 
        }
      });

      if (!progress) {
        throw new Error('Student progress not found');
      }

      const updateData = {};
      if (progressData.progressPercentage !== undefined) {
        updateData.progress_percentage = progressData.progressPercentage;
      }
      if (progressData.currentPhase !== undefined) {
        updateData.current_phase = progressData.currentPhase;
      }
      if (progressData.timeSpentHours !== undefined) {
        updateData.time_spent_hours = progressData.timeSpentHours;
      }
      if (progressData.status !== undefined) {
        updateData.status = progressData.status;
      }
      if (progressData.startDate !== undefined) {
        updateData.start_date = progressData.startDate;
      }
      if (progressData.completionDate !== undefined) {
        updateData.completion_date = progressData.completionDate;
      }

      updateData.last_activity = new Date();

      await progress.update(updateData);

      logger.info(`Updated student progress for project ${projectId}, student ${studentId}`);
      return progress;
    } catch (error) {
      logger.error('Error updating student progress:', error);
      throw error;
    }
  }

  /**
   * Get student project statistics
   */
  async getStudentProjectStats(studentId, courseId = null) {
    try {
      const whereClause = { student_id: studentId };
      if (courseId) {
        whereClause.course_id = courseId;
      }

      const progressRecords = await StudentProjectProgress.findAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'due_date', 'status']
          }
        ]
      });

      const stats = {
        totalProjects: progressRecords.length,
        notStarted: progressRecords.filter(p => p.status === 'not_started').length,
        inProgress: progressRecords.filter(p => p.status === 'in_progress').length,
        completed: progressRecords.filter(p => p.status === 'completed').length,
        overdue: progressRecords.filter(p => p.status === 'overdue').length,
        averageProgress: progressRecords.length > 0 ? 
          progressRecords.reduce((sum, p) => sum + parseFloat(p.progress_percentage), 0) / progressRecords.length : 
          0,
        totalTimeSpent: progressRecords.reduce((sum, p) => sum + parseFloat(p.time_spent_hours), 0),
        averageGrade: progressRecords.filter(p => p.grade).length > 0 ?
          progressRecords.filter(p => p.grade).reduce((sum, p) => sum + parseFloat(p.grade), 0) / 
          progressRecords.filter(p => p.grade).length : 
          null
      };

      return stats;
    } catch (error) {
      logger.error('Error getting student project stats:', error);
      throw error;
    }
  }
}

export default new StudentProjectService();
