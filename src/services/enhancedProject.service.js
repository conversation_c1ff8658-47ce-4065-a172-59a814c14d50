import {
  Project,
  ProjectTemplate,
  ProjectAssignment,
  User,
  LtiContext,
  Rubric,
  Submission,
  LtiContextEnrollment,
  Grade,
  Checkpoint
} from '../models/associations.js';
import { Op, or, Sequelize } from 'sequelize';
import { sequelize } from '../config/database.config.js';
import logger from '../config/logger.config.js';
import courseRoleService from './courseRole.service.js';
import { checkAccessRole, getSortOrderList } from '../utils/helpers.utils.js';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';
import enhancedProjectUtils from '../utils/enhancedProject.utils.js';
import enhancedGradesService from '../services/enhancedGrades.service.js';
import s3Service from './s3.service.js';
class EnhancedProjectService {
  /**
   * Format date to DD-MM-YYYY format
   */
  formatDateToDDMMYYYY(date) {
    if (!date) return null;
    return new Date(date)
      .toLocaleDateString('en-GB')
      .split('/')
      .reverse()
      .join('-');
  }

  async creationOfProject(req) {
    const {
      title,
      description,
      courseId,
      projectType = 'individual',
      difficulty_level = 'beginner',
      estimatedHours,
      totalPoints = 100,
      dueDate,
      startDate,
      instructions,
      project_overview,
      learning_objectives,
      prerequisites,
      tags = [],
      isTemplate = false,
      templateCategory = 'general',
      templateSubcategory,
      template_id,
      assignments = [],
      isScreen,
      status = 'draft',
      id,
      categoryId = '3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66',
      instructorIds = [],
      teachingAssId = [],
      maxSubmissions,
      lateSubmissionsAllowed = false,
      sandbox_time_duration,
      late_submission_days_allowed
    } = req.body;

    // Validate required fields
    // if (!title || !description || !contextId)
    if (!title)
      throw new ApiError(httpStatus.BAD_REQUEST, 'Project Title is required');

    // Validate user can create projects in this context

    if (await checkAccessRole(req.primaryRole, req.userRoles, 'studentOnly')) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'Permission denied: Students cannot create projects'
      );
    }

    /* if (!(await checkAccessRole(req.primaryRole, req.userRoles, 'adminOnly'))) {
      try {
        if (contextId) {
          const permission = await courseRoleService.canCreateProjects(
            req.user.id,
            contextId
          );
          if (!permission.canCreate)
            throw new ApiError(httpStatus.FORBIDDEN, permission.reason);
        }
      } catch (error) {
        throw error;
      }
    } */

    try {
      // Update project
      const projectData = {};
      if (title) projectData.title = title;
      if (categoryId) projectData.category_id = categoryId;
      if (courseId) projectData.course_id = courseId;
      if (description) projectData.description = description;
      if (instructorIds) projectData.instructor_id = instructorIds;
      if (teachingAssId) projectData.teaching_ass_id = teachingAssId;
      if (projectType) projectData.type = projectType;
      if (difficulty_level) projectData.difficulty_level = difficulty_level;
      if (totalPoints) projectData.total_points = totalPoints;
      if (estimatedHours) projectData.estimated_hours = estimatedHours;
      if (tags) projectData.tags = tags;
      if (project_overview) projectData.project_overview = project_overview;
      if (learning_objectives)
        projectData.learning_objectives = learning_objectives;
      if (prerequisites) projectData.prerequisites = prerequisites;
      if (instructions) projectData.instructions = instructions;
      // if (resources) projectData.resources = resources;
      if (maxSubmissions) projectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed)
        projectData.late_submission_allowed = lateSubmissionsAllowed;
      if (dueDate) projectData.due_date = dueDate;
      if (status) projectData.status = status;
      if (isScreen) projectData.isScreen = isScreen;
      if (startDate) projectData.start_date = startDate;
      if (isTemplate) projectData.is_template = isTemplate;
      if (templateCategory) projectData.template_category = templateCategory;
      if (templateSubcategory)
        projectData.template_subcategory = templateSubcategory;
      if (late_submission_days_allowed)
        projectData.late_submission_days_allowed = late_submission_days_allowed;
      if (sandbox_time_duration)
        projectData.sandbox_time_duration = sandbox_time_duration;
      if (template_id) projectData.template_id = template_id;

      // if (settings) projectData.settings = settings;

      if (req.user.id) {
        projectData.created_by = req.user.id;
      }

      let project;
      if (isScreen > 1 || id) {
        projectData.id = id;
        project = await this.updateProject(projectData, assignments);
      } else {
        project = await this.createProject(projectData, assignments);
      }

      return {
        id: project.id,
        projectId: project.project_code,
        title: project.title,
        status: project.status,
        courseId: project.course_id,
        projectType: project.project_type,
        totalPoints: project.total_points,
        isTemplate: project.is_template,
        createdAt: project.createdAt,
        isScreen: project.isScreen,
        projectTemplate: project.templateDetails || {}
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new project with enhanced fields
   */
  async createProject(projectData, assignments = []) {
    try {
      const project = await Project.create(projectData);

      /* // Create project assignments if provided
      if (assignments.length > 0) {
        const assignmentPromises = assignments.map(assignment =>
          ProjectAssignment.create({
            ...assignment,
            project_id: project.id,
            assigned_by: projectData.created_by
          })
        );
        await Promise.all(assignmentPromises);
      } */

      logger.info(
        `Enhanced project created: ${project.title} by user ${projectData.created_by}`
      );

      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update a project with enhanced fields
   */
  async updateProject(projectData) {
    try {
      const project = await Project.findOne({
        where: { id: projectData.id, is_deleted: false },
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ]
      });

      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      if (project && project.status === 'published')
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Published project cannot be updated'
        );

      await project.update(projectData);

      logger.info(
        `Project updated: ${projectData.title} by user ${projectData.created_by}`
      );

      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new project with enhanced fields
   */
  async getEnhancedProject(req) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        courseId,
        status,
        difficultyLevel,
        isLinked,
        sortBy,
        sortOrder
      } = req.query;

      const offset = (parseInt(page, 10) - 1) * parseInt(limit, 10);
      const isAdmin = req.userRoles?.includes('admin');
      const isStudent = req.userRoles?.includes('student') && !isAdmin;

      // ---------- base filters (search, course, difficulty) ----------
      const baseWhere = { is_deleted: false, is_template: false };

      if (search) {
        baseWhere[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ];
      }

      if (courseId) baseWhere.course_id = courseId;
      if (difficultyLevel) baseWhere.difficulty_level = difficultyLevel;

      // ---------- include clause (course, instructor, creator) ----------
      const includeClause = [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }
      ];

      // ---------- role-based visibility ----------
      let finalWhere = { ...baseWhere };

      if (isStudent) {
        // Students: only published & must be enrolled
        finalWhere.status = 'published';

        // Add enrollment check through course relationship
        includeClause[0].include = [
          {
            model: LtiContextEnrollment,
            as: 'enrollments',
            where: { user_id: req.user.id },
            required: false,
            attributes: [
              'id',
              'user_id',
              'context_id',
              'role_in_course',
              'enrollment_status'
            ]
          }
        ];
      } else {
        // Non-students (admin, instructor, TA)

        if (status) {
          if (status === 'published') {
            finalWhere.status = 'published';
          } else if (status === 'draft') {
            // Only my drafts
            finalWhere = {
              ...baseWhere,
              status: 'draft',
              created_by: req.user.id
            };
          } else if (status === 'archived') {
            // archived (admins see all; non-admins limited by enrollment include below)
            finalWhere.status = 'archived';
          } else {
            // fallback if some unknown status
            finalWhere = { ...baseWhere, status };
          }
        } else {
          // default: published OR my drafts
          finalWhere[Op.or] = [
            { status: 'published' },
            { [Op.and]: [{ status: 'draft' }, { created_by: req.user.id }] }
          ];
        }

        // Non-admins still need enrollment check through course relationship
        if (!isAdmin) {
          includeClause[0].include = [
            {
              model: LtiContextEnrollment,
              as: 'enrollments',
              where: { user_id: req.user.id },
              required: false,
              attributes: [
                'id',
                'user_id',
                'context_id',
                'role_in_course',
                'enrollment_status'
              ]
            }
          ];
        }
      }

      // ---------- isLinked filtering logic ----------
      if (isLinked !== undefined) {
        finalWhere.is_linked = isLinked === true;
      }

      // ---------- query ----------
      const safeSortOrder = await getSortOrderList(sortBy, sortOrder, [
        ['updated_at', 'DESC']
      ]);

      const { count, rows: projects } = await Project.findAndCountAll({
        where: finalWhere,
        include: includeClause,
        limit: parseInt(limit),
        offset,
        order: safeSortOrder,
        distinct: true
      });

      // ---------- bulk fetch instructor user objects for all projects ----------
      /*  let instructorsById = {};
      try {
        const allInstructorIds = Array.from(
          new Set(
            projects
              .flatMap(p =>
                Array.isArray(p.instructor_id) ? p.instructor_id : []
              )
              .filter(Boolean)
          )
        );
        if (allInstructorIds.length) {
          const instructorUsers = await User.findAll({
            where: { id: { [Op.in]: allInstructorIds } },
            attributes: ['id', 'name', 'email']
          });
          instructorsById = instructorUsers.reduce((acc, u) => {
            acc[u.id] = u;
            return acc;
          }, {});
        }
      } catch (e) {
        logger.warn('Failed to prefetch instructor users', e);
      } */

      let transformedProjects;

      // Transform project data for response
      if (projects.length > 0) {
        transformedProjects = await Promise.all(
          projects.map(async project => {
            // Get submission count for this project
            const submissionCount = await Submission.count({
              where: { project_id: project.id },
              // Ensure soft-deletes don't inject deleted_at filter on a table without that column
              paranoid: false
            });

            // Get user's submission if exists
            /*   let userSubmission = null;
            if (req.user && !req.userRoles.includes('admin')) {
              userSubmission = await Submission.findOne({
                where: { project_id: project.id, user_id: req.user.id },
                attributes: ['id', 'status', 'submitted_at'],
                paranoid: false
              });
            } */

            return {
              id: project.id,
              projectId: project.project_code,
              title: project.title,
              description: project.description,
              status: project.status,
              difficultyLevel: project.difficulty_level,
              startDate: project.start_date,
              dueDate: this.formatDateToDDMMYYYY(project.due_date),
              totalPoints: project.total_points,
              // instructions: project.instructions,
              isLinked: project.is_linked,
              submissionCount,
              // userSubmission,
              /*  instructors: Array.isArray(project.instructor_id)
                ? project.instructor_id
                    .map(iid => instructorsById[iid])
                    .filter(Boolean)
                    .map(u =>
                      u ? { id: u.id, name: u.name, email: u.email } : null
                    )
                    .filter(Boolean)
                : [], */
              course: project.course
                ? {
                    id: project.course.id,
                    name:
                      project.course.dataValues?.name || project.course.name,
                    code: project.course.dataValues?.code || project.course.code
                  }
                : {}
              /*  creator: {
             id: project.creator.id,
             name: project.creator.name,
             email: project.creator.email
           },
           createdAt: project.created_at,
           updatedAt: project.updated_at */
            };
          })
        );
      }

      return {
        projects: transformedProjects,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get project by ID with all details
   * Get project with full details including assignments and template
   */
  async getProjectWithDetails(req) {
    try {
      const { id } = req.params;

      // Validate required fields
      if (!id)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');

      // Validate user can update projects in this course
      const getProjectDetailsById =
        await enhancedProjectUtils.checkProjectExist(id);
      if (!getProjectDetailsById)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      if (
        getProjectDetailsById.status === 'draft' &&
        getProjectDetailsById.created_by !== req.user.id
      )
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Draft project can be accessed only by creator'
        );

      const includeClause = [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
          raw: true
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: Checkpoint,
          as: 'checkpoints',
          required: false,
          attributes: [
            'id',
            'title',
            'description',
            'checkpoint_number',
            'start_date',
            'due_date',
            'status',
            'is_required'
          ],

          include: [
            {
              model: Rubric,
              as: 'rubrics',
              required: false,
              attributes: [
                'id',
                'title',
                'description',
                'criteria',
                'total_points',
                'is_template',
                'template_name',
                'created_at'
              ],
              include: [
                {
                  model: User,
                  as: 'creator',
                  attributes: ['id', 'name', 'email']
                }
              ]
            }
          ]
        },
        {
          model: ProjectTemplate,
          as: 'template',
          required: false
        }
        /* {
          model: ProjectAssignment,
          as: 'assignments',
          required: false,
          include: [
            {
              model: User,
              as: 'assignedUser',
              attributes: ['id', 'name', 'email']
            }
          ]
        } */
      ];

      /*  if (await checkAccessRole(req.primaryRole, req.userRoles, 'studentOnly')) { }
       if (await checkAccessRole(req.primaryRole, req.userRoles, 'instructorOnly')) {
         includeClause.push({
           model: CourseEnrollment,
           as: 'courseEnrollments',
         });
       }
  */

      const project = await Project.findOne({
        where: { id, is_deleted: false },
        include: includeClause,
        order: [
          [{ model: Checkpoint, as: 'checkpoints' }, 'checkpoint_number', 'ASC']
        ]
      });

      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }

      /*   // Get assignment statistics
      let assignmentStats = {};
      try {
        assignmentStats = await this.getProjectAssignmentStats(id);
      } catch (error) {
        logger.warn('Error getting assignment stats:', error);
        assignmentStats = { totalAssignments: 0, byRole: {}, roles: [] };
      }

      // Add assignment statistics to project
      project.dataValues.assignmentStats = assignmentStats; */

      // Get user's submission if exists
      let userSubmission = null;
      let checkpointScores = [];
      let checkpointStatistics = [];
      const isStudent = await checkAccessRole(
        req.primaryRole,
        req.userRoles,
        'studentOnly'
      );

      if (isStudent) {
        userSubmission = await Submission.findAndCountAll({
          where: { project_id: project.id, user_id: req.user.id },
          attributes: ['id', 'status', 'submitted_at', 'user_id', 'project_id'],
          include: [
            {
              model: Grade,
              as: 'grade',
              paranoid: false,
              required: false,
              include: [
                {
                  model: User,
                  as: 'evaluator',
                  attributes: ['id', 'name', 'email']
                }
              ]
            }
          ]
        });

        checkpointScores = await Promise.all(
          userSubmission.rows.map(async submission => {
            if (submission.grade && submission.grade.rubric_scores) {
              return await enhancedGradesService.transformRubricScoresToCheckpoints(
                submission.grade.rubric_scores,
                project.id,
                submission.user_id,
                isStudent
              );
            }
          })
        );
      } else {
        try {
          userSubmission = await Submission.findAndCountAll({
            where: { project_id: project.id },
            attributes: [
              'id',
              'status',
              'submitted_at',
              'user_id',
              'project_id'
            ],
            include: [
              {
                model: Grade,
                as: 'grade',
                paranoid: false,
                required: false,
                include: [
                  {
                    model: User,
                    as: 'evaluator',
                    attributes: ['id', 'name', 'email']
                  }
                ]
              }
            ]
          });

          /* checkpointScores = await Promise.all(
            userSubmission.rows.map(async submission => {
              if (submission.grade && submission.grade.rubric_scores) {
                return await enhancedGradesService.transformRubricScoresToCheckpoints(
                  submission.grade.rubric_scores,
                  project.id,
                  submission.user_id,
                  isStudent
                );
              }
            })
          ); */
        } catch (err) {
          logger.warn(`Error getting user submissions: ${err.message}`);
          // throw err;
        }

        // Get checkpoint statistics for instructors
        try {
          checkpointStatistics =
            await enhancedGradesService.getCheckpointStatistics(project.id);
        } catch (err) {
          logger.warn(`Error getting checkpoint statistics: ${err.message}`);
          checkpointStatistics = [];
        }
      }

      // Fetch instructor and teaching assistant user details
      let instructors = [];
      let teachingAssistants = [];

      if (
        Array.isArray(project.instructor_id) &&
        project.instructor_id.length > 0
      ) {
        instructors = await User.findAll({
          where: { id: project.instructor_id },
          attributes: ['id', 'name', 'email', 'status']
        });
      }

      if (
        Array.isArray(project.teaching_ass_id) &&
        project.teaching_ass_id.length > 0
      ) {
        teachingAssistants = await User.findAll({
          where: { id: project.teaching_ass_id },
          attributes: ['id', 'name', 'email', 'status']
        });
      }

      // ...after fetching the project and before returning projectData...
      const courseId = project.course_id;
      let students = [];
      if (courseId) {
        students = await User.findAll({
          include: [
            {
              model: LtiContextEnrollment,
              as: 'enrollments',
              where: { context_id: courseId, role_in_course: 'student' },
              attributes: []
            }
          ],
          attributes: ['id', 'name', 'email', 'status']
        });
      }

      // After fetching the project and before building projectData
      let datasetS3Details = [];
      if (
        Array.isArray(project.dataset_s3_url) &&
        project.dataset_s3_url.length > 0
      ) {
        try {
          datasetS3Details = await Promise.all(
            project.dataset_s3_url.map(async key => {
              try {
                // Fetch presigned URL and metadata for each S3 key
                return await s3Service.generatePresignedDownloadUrl(
                  key,
                  3600,
                  true
                );
              } catch (err) {
                // Optionally log or handle missing files
                logger.warn(`Could not fetch S3 details for key: ${key}`);
                return { key, error: 'Not found or inaccessible' };
              }
            })
          );
        } catch (error) {
          logger.warn('Error processing S3 dataset URLs:', error);
          datasetS3Details = [];
        }
      }

      const projectDetails = project.toJSON();

      // Merge checkpoint statistics with project checkpoints
      if (
        checkpointStatistics &&
        checkpointStatistics.length > 0 &&
        projectDetails.checkpoints
      ) {
        projectDetails.checkpoints = projectDetails.checkpoints.map(
          checkpoint => {
            const stats = checkpointStatistics.find(
              stat => stat.checkpointId === checkpoint.id
            );
            const statisticsDetails = stats
              ? {
                  totalStudents: stats.totalStudents,
                  totalSubmissions: stats.totalSubmissions,
                  submittedCount: stats.submittedCount,
                  inProgressCount: stats.inProgressCount,
                  notStartedCount: stats.notStartedCount,
                  gradedCount: stats.gradedCount
                }
              : {
                  totalStudents: 0,
                  totalSubmissions: 0,
                  submittedCount: 0,
                  inProgressCount: 0,
                  notStartedCount: 0,
                  gradedCount: 0
                };
            return {
              ...checkpoint,
              ...statisticsDetails
            };
          }
        );
      } else if (checkpointScores && checkpointScores.length > 0) {
        projectDetails.checkpoints = checkpointScores;
      }

      // Add to response
      const projectData = {
        ...projectDetails,
        instructors,
        teachingAssistants,
        students,
        Submission: userSubmission,
        dataset_s3_url: datasetS3Details
      };

      return projectData;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Publish project
   */
  async publishProject(projectId, publisherId, role) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, is_deleted: false }
      });

      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      if (role === 'student') {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Students cannot publish projects'
        );
      }

      if (project.status === 'draft' && project.created_by !== publisherId) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only the project creator can publish a draft project'
        );
      }

      if (project.status === 'published') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project already published');
      }

      if (project.status === 'archived') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project already archived');
      }

      /* if(!project.category_id){
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a category assigned before publishing'
        );
      } */

      if (!project.course_id) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must be assigned to a course'
        );
      }

      if (!project.instructor_id || project.instructor_id.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have at least one instructor assigned before publishing'
        );
      }

      /*   if (!project.teaching_ass_id || project.teaching_ass_id.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have at least one teaching assistant assigned before publishing'
        );
      } */

      // Checkpoint validation
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId }
      });

      if (!checkpoints || checkpoints.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have at least one checkpoint before publishing'
        );
      }

      // Validate each checkpoint has mandatory fields
      for (const checkpoint of checkpoints) {
        if (!checkpoint.start_date) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            `Checkpoint "${checkpoint.title}" must have a start date before publishing`
          );
        }
        if (!checkpoint.due_date) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            `Checkpoint "${checkpoint.title}" must have a due date before publishing`
          );
        }

        if (checkpoint.start_date > checkpoint.due_date) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'checkpoint start date cannot be after end date'
          );
        }
      }

      const rubricData = await Rubric.findOne({
        where: { project_id: projectId }
      });
      if (!rubricData) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a rubric assigned before publishing'
        );
      }

      /*  if (!project.start_date) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a start date assigned before publishing'
        );
      } */

      if (!project.due_date) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a end date assigned before publishing'
        );
      }

      await project.update({
        status: 'published',
        published_at: new Date(),
        published_by: publisherId
      });

      logger.info(`Project published: ${project.title} by user ${publisherId}`);
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Unpublish project
   */
  async unpublishProject(projectId, userId, role) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, is_deleted: false }
      });

      if (role === 'student') {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Students cannot unpublish projects'
        );
      }

      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      if (project.status !== 'published') {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Only published projects can be unpublished'
        );
      }

      // Check permissions
      if (project.created_by !== userId && project.published_by !== userId) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only creator or publisher can unpublish'
        );
      }

      await project.update({
        status: 'draft',
        published_at: null,
        published_by: null
      });

      logger.info(`Project unpublished: ${project.title} by user ${userId}`);
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * @desc    Duplicate project
   * @route   POST /api/projects/:id/duplicate
   * @access  Private (Instructor, Admin)
   */
  async duplicateProject(req) {
    try {
      const { id } = req.params;
      const { courseId, title } = req.body;

      const originalProject = await Project.findOne({
        where: { id, is_deleted: false },
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          },
          {
            model: Checkpoint,
            as: 'checkpoints',
            include: [
              {
                model: Rubric,
                as: 'rubrics',
                required: false
              }
            ]
          },
          {
            model: Rubric,
            as: 'rubrics'
          }
        ]
      });

      if (!originalProject)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      // Check permissions for original project
      const hasPermission =
        req.userRoles.includes('admin') ||
        originalProject.created_by === req.user.id;

      if (!hasPermission)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to duplicate this project'
        );

      if (courseId || originalProject.course_id) {
        // Check permission for target course
        const targetContext = await LtiContext.findByPk(
          courseId || originalProject.course_id
        );
        if (!targetContext)
          throw new ApiError(httpStatus.NOT_FOUND, 'Target course not found');

        const targetEnrollment = await LtiContextEnrollment.findOne({
          where: {
            context_id: courseId || originalProject.course_id,
            user_id: req.user.id
          }
        });

        const hasTargetPermission =
          req.userRoles.includes('admin') ||
          (targetEnrollment &&
            ['instructor', 'admin'].includes(targetEnrollment.role_in_course));

        if (!hasTargetPermission)
          throw new ApiError(
            httpStatus.FORBIDDEN,
            'You do not have permission to create projects in the target course'
          );
      }

      // Create duplicate project
      const duplicateProject = await Project.create({
        title: title || `${originalProject.title} (Copy)`,
        description: originalProject.description,
        course_id: courseId || originalProject.course_id || null,
        created_by: req.user.id,
        difficulty_level: originalProject.difficulty_level,
        estimated_hours: originalProject.estimated_hours,
        due_date: originalProject.due_date,
        instructions: originalProject.instructions,
        requirements: originalProject.requirements,
        resources: originalProject.resources,
        settings: originalProject.settings,
        status: 'draft',
        isScreen: originalProject.isScreen
      });

      // Duplicate checkpoints with their rubrics
      if (
        originalProject.checkpoints &&
        originalProject.checkpoints.length > 0
      ) {
        for (const checkpoint of originalProject.checkpoints) {
          const newCheckpoint = await Checkpoint.create({
            project_id: duplicateProject.id,
            title: checkpoint.title,
            description: checkpoint.description,
            checkpoint_number: checkpoint.checkpoint_number,
            due_date: checkpoint.due_date,
            start_date: checkpoint.start_date,
            weight_percentage: checkpoint.weight_percentage,
            created_by: req.user.id,
            is_required: checkpoint.is_required,
            status: 'draft',
            metadata: checkpoint.metadata
          });

          // Duplicate rubrics for this checkpoint
          if (checkpoint.rubrics && checkpoint.rubrics.length > 0) {
            const rubricPromises = checkpoint.rubrics.map(rubric =>
              Rubric.create({
                project_id: duplicateProject.id,
                checkpoint_id: newCheckpoint.id,
                title: rubric.title,
                description: rubric.description,
                criteria: rubric.criteria,
                total_points: rubric.total_points,
                grading_scale: rubric.grading_scale,
                is_template: rubric.is_template,
                template_name: rubric.template_name,
                created_by: req.user.id
              })
            );
            await Promise.all(rubricPromises);
          }
        }
      }

      // Duplicate project-level rubrics (if any exist without checkpoint association)
      if (originalProject.rubrics && originalProject.rubrics.length > 0) {
        const projectRubrics = originalProject.rubrics.filter(
          rubric => !rubric.checkpoint_id
        );
        if (projectRubrics.length > 0) {
          const rubricPromises = projectRubrics.map(rubric =>
            Rubric.create({
              project_id: duplicateProject.id,
              checkpoint_id: null,
              title: rubric.title,
              description: rubric.description,
              criteria: rubric.criteria,
              total_points: rubric.total_points,
              grading_scale: rubric.grading_scale,
              is_template: rubric.is_template,
              template_name: rubric.template_name,
              created_by: req.user.id
            })
          );
          await Promise.all(rubricPromises);
        }
      }

      logger.info(
        `Project duplicated: ${originalProject.title} to ${duplicateProject.title} by ${req.user.email}`
      );
      return {
        id: duplicateProject.id,
        title: duplicateProject.title,
        description: duplicateProject.description,
        status: duplicateProject.status,
        isScreen: duplicateProject.isScreen,
        projectId: duplicateProject.project_code,
        courseId: duplicateProject.course_id,
        createdAt: duplicateProject.created_at
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * @desc    Delete project
   * @route   DELETE /api/projects/:id
   * @access  Private (Instructor, Admin)
   */
  async deleteProject(req) {
    try {
      const { id } = req.params;

      const project = await Project.findOne({
        where: { id, is_deleted: false },
        include: [
          {
            model: LtiContext,
            as: 'course'
          }
        ]
      });

      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      if (project.status === 'published')
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Cannot delete a published project. Please unpublish first.'
        );

      // Check permissions
      const hasPermission =
        req.userRoles.includes('admin') || project.created_by === req.user.id;

      if (!hasPermission)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to delete this project'
        );

      // Check if project has submissions
      const submissionCount = await Submission.count({
        where: { project_id: id }
      });

      if (submissionCount > 0)
        throw new ApiError(
          httpStatus.CONFLICT,
          'Cannot delete project with existing submissions'
        );

      // Soft delete project
      await project.update({
        is_deleted: true,
        deleted_by: req.user.id,
        deleted_at: new Date()
      });

      logger.info(`Project deleted: ${project.title} by ${req.user.email}`);

      return { message: 'Project deleted successfully' };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Updation of the project
   */
  async UpdationOfProject(req) {
    const {
      title,
      description,
      courseId,
      projectType = 'individual',
      difficulty_level = 'beginner',
      estimatedHours,
      totalPoints = 100,
      dueDate,
      startDate,
      instructions,
      project_overview,
      learning_objectives,
      prerequisites,
      skillsCovered = [],
      technologiesUsed = [],
      tags = [],
      isTemplate = false,
      templateCategory = 'general',
      templateSubcategory,
      assignments = [],
      rubrics = [],
      categoryId = '3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66',
      instructorIds = [],
      teachingAssId = [],
      maxSubmissions,
      lateSubmissionsAllowed = false,
      sandbox_time_duration,
      late_submission_days_allowed
    } = req.body;

    const { id } = req.params;

    // Validate required fields
    if (!id)
      throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');
    if (!title)
      throw new ApiError(httpStatus.BAD_REQUEST, 'Title are required');

    // Validate user can update projects in this course
    const updateProjectDetails =
      await enhancedProjectUtils.checkProjectExist(id);
    if (!updateProjectDetails)
      throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
    if (updateProjectDetails.status === 'published')
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Published project cannot be updated'
      );
    if (updateProjectDetails.created_by !== req.user.id)
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'Only project creator can update the project'
      );
    try {
      // Update project
      const projectData = {};
      if (title) projectData.title = title;
      if (categoryId) projectData.category_id = categoryId;
      if (courseId) projectData.course_id = courseId;
      if (description) projectData.description = description;
      if (instructorIds) projectData.instructor_id = instructorIds;
      if (teachingAssId) projectData.teaching_ass_id = teachingAssId;
      if (projectType) projectData.type = projectType;
      if (difficulty_level) projectData.difficulty_level = difficulty_level;
      if (totalPoints) projectData.total_points = totalPoints;
      if (estimatedHours) projectData.estimated_hours = estimatedHours;
      if (tags) projectData.tags = tags;
      if (project_overview) projectData.project_overview = project_overview;
      if (learning_objectives)
        projectData.learning_objectives = learning_objectives;
      if (prerequisites) projectData.prerequisites = prerequisites;
      if (instructions) projectData.instructions = instructions;
      // if (resources) projectData.resources = resources;
      if (maxSubmissions) projectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed)
        projectData.late_submission_allowed = lateSubmissionsAllowed;
      if (dueDate) projectData.due_date = dueDate;
      if (startDate) projectData.start_date = startDate;
      if (isTemplate) projectData.is_template = isTemplate;
      if (templateCategory) projectData.template_category = templateCategory;
      if (templateSubcategory)
        projectData.template_subcategory = templateSubcategory;
      if (late_submission_days_allowed)
        projectData.late_submission_days_allowed = late_submission_days_allowed;
      if (sandbox_time_duration)
        projectData.sandbox_time_duration = sandbox_time_duration;

      // if (settings) projectData.settings = settings;

      projectData.id = id;
      const project = await this.updateProject(projectData, assignments);

      return {
        id: project.id,
        projectId: project.project_code,
        title: project.title,
        status: project.status,
        courseId: project.course_id,
        projectType: project.project_type,
        createdAt: project.createdAt
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create project template
   */
  async createProjectTemplate(req) {
    const transaction = await sequelize.transaction();

    try {
      const {
        title,
        description,
        courseId,
        projectType = 'individual',
        difficulty_level = 'beginner',
        estimatedHours,
        totalPoints = 100,
        dueDate,
        startDate,
        instructions,
        project_overview,
        learning_objectives,
        prerequisites,
        tags = [],
        isTemplate = false,
        templateCategory = 'general',
        templateSubcategory,
        isScreen = 4,
        status = 'draft',
        instructorIds = [],
        teachingAssId = [],
        maxSubmissions,
        lateSubmissionsAllowed = false,
        sandbox_time_duration,
        late_submission_days_allowed,
        technologies_used,
        skillsCovered
      } = req.body;

      const templateProjectData = {};
      if (title) {
        templateProjectData.title = title;
        templateProjectData.template_name = title;
      }
      if (courseId) templateProjectData.course_id = courseId;
      if (description) {
        templateProjectData.description = description;
        templateProjectData.template_description = description;
      }
      if (instructorIds) templateProjectData.instructor_id = instructorIds;
      if (teachingAssId) templateProjectData.teaching_ass_id = teachingAssId;
      if (projectType) templateProjectData.type = projectType;
      if (difficulty_level)
        templateProjectData.difficulty_level = difficulty_level;
      if (totalPoints) templateProjectData.total_points = totalPoints;
      if (estimatedHours) templateProjectData.estimated_hours = estimatedHours;
      if (tags) templateProjectData.tags = tags;
      if (project_overview)
        templateProjectData.project_overview = project_overview;
      if (learning_objectives)
        templateProjectData.learning_objectives = learning_objectives;
      if (prerequisites) templateProjectData.prerequisites = prerequisites;
      if (instructions) templateProjectData.instructions = instructions;
      if (maxSubmissions) templateProjectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed)
        templateProjectData.late_submission_allowed = lateSubmissionsAllowed;
      if (dueDate) templateProjectData.due_date = dueDate;
      if (status) templateProjectData.status = status;
      if (isScreen) templateProjectData.isScreen = isScreen;
      if (startDate) templateProjectData.start_date = startDate;
      if (isTemplate) templateProjectData.is_template = isTemplate;
      if (templateCategory) templateProjectData.category = templateCategory;
      if (templateSubcategory)
        templateProjectData.subcategory = templateSubcategory;
      if (late_submission_days_allowed)
        templateProjectData.late_submission_days_allowed =
          late_submission_days_allowed;
      if (sandbox_time_duration)
        templateProjectData.sandbox_time_duration = sandbox_time_duration;
      if (skillsCovered) templateProjectData.skills_covered = skillsCovered;
      if (technologies_used)
        templateProjectData.technologies_used = technologies_used;

      templateProjectData.created_by = req.user.id;

      const newTemplateProject = await Project.create(templateProjectData, {
        transaction
      });

      const templateData = {
        project_id: newTemplateProject.id,
        created_by: req.user.id
      };
      if (title) templateData.template_name = title;
      if (description) templateData.template_description = description;
      if (templateCategory) templateData.category = templateCategory;
      if (templateSubcategory) templateData.subcategory = templateSubcategory;
      if (difficulty_level) templateData.difficulty_level = difficulty_level;
      if (estimatedHours) templateData.estimated_hours = estimatedHours;
      if (totalPoints) templateData.total_points = totalPoints;
      if (learning_objectives)
        templateData.learning_objectives = learning_objectives;
      if (prerequisites) templateData.prerequisites = prerequisites;
      if (skillsCovered) templateData.skills_covered = skillsCovered;
      if (technologies_used) templateData.technologies_used = technologies_used;
      if (tags) templateData.tags = tags;

      const template = await ProjectTemplate.create(templateData, {
        transaction
      });

      await transaction.commit();

      logger.info(`Project template created: ${template.template_name}`);

      return {
        id: template.id,
        title: template.template_name,
        description: template.template_description,
        template_category: template.category,
        template_subcategory: template.subcategory,
        difficulty_level: template.difficulty_level,
        estimated_hours: template.estimated_hours,
        total_points: template.total_points,
        learning_objectives: template.learning_objectives,
        prerequisites: template.prerequisites,
        skills_covered: template.skills_covered,
        technologies_used: template.technologies_used,
        tags: template.tags,
        isPublic: template.is_public,
        createdAt: template.createdAt,
        projectId: newTemplateProject.id
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Create template from existing project
   */
  async createTemplateFromProject(req) {
    const transaction = await sequelize.transaction();

    try {
      const { projectId } = req.params;
      const {
        title,
        description,
        category,
        subcategory,
        skills_covered,
        technologies_used
      } = req.body;

      const userId = req.user.id;

      // Validate request data
      if (!projectId) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid request data');
      }

      // Get complete project details with checkpoints and rubrics
      const originalProject = await Project.findOne({
        where: { id: projectId, is_deleted: false },
        include: [
          {
            model: Checkpoint,
            as: 'checkpoints',
            required: false,
            include: [
              {
                model: Rubric,
                as: 'rubrics',
                required: false
              }
            ]
          }
        ],
        transaction
      });

      if (!originalProject) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      // Check permissions
      const hasPermission =
        userId === originalProject.created_by ||
        req.userRoles?.includes('admin');

      if (!hasPermission) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only project creator or admin can create template'
        );
      }

      // Create new project for template
      const templateProject = await Project.create(
        {
          title: title || `${originalProject.title} (Template)`,
          description: description || originalProject.description,
          course_id: null, // Templates don't belong to specific courses
          instructions: originalProject.instructions,
          difficulty_level: originalProject.difficulty_level,
          estimated_hours: originalProject.estimated_hours,
          total_points: originalProject.total_points,
          learning_objectives: originalProject.learning_objectives,
          prerequisites: originalProject.prerequisites,
          project_overview: originalProject.project_overview,
          tags: originalProject.tags,
          type: originalProject.type,
          category_id: originalProject.category_id,
          created_by: userId,
          status: 'draft',
          is_template: true,
          template_name: title || `${originalProject.title} (Template)`
        },
        { transaction }
      );

      // Duplicate checkpoints with their rubrics
      if (
        originalProject.checkpoints &&
        originalProject.checkpoints.length > 0
      ) {
        for (const checkpoint of originalProject.checkpoints) {
          const newCheckpoint = await Checkpoint.create(
            {
              project_id: templateProject.id,
              title: checkpoint.title,
              description: checkpoint.description,
              checkpoint_number: checkpoint.checkpoint_number,
              due_date: checkpoint.due_date,
              start_date: checkpoint.start_date,
              weight_percentage: checkpoint.weight_percentage,
              is_required: checkpoint.is_required,
              status: 'draft',
              metadata: checkpoint.metadata,
              is_template: true,
              template_name: title || `${originalProject.title} (Template)`,
              created_by: userId
            },
            { transaction }
          );

          // Duplicate rubrics for this checkpoint
          if (checkpoint.rubrics && checkpoint.rubrics.length > 0) {
            const rubricPromises = checkpoint.rubrics.map(rubric =>
              Rubric.create(
                {
                  project_id: templateProject.id,
                  checkpoint_id: newCheckpoint.id,
                  title: rubric.title,
                  description: rubric.description,
                  criteria: rubric.criteria,
                  total_points: rubric.total_points,
                  grading_scale: rubric.grading_scale,
                  is_template: true,
                  template_name: title || `${originalProject.title} (Template)`,
                  created_by: userId
                },
                { transaction }
              )
            );
            await Promise.all(rubricPromises);
          }
        }
      }

      // Create project template record
      const template = await ProjectTemplate.create(
        {
          project_id: templateProject.id,
          template_name: title || `${originalProject.title} (Template)`,
          template_description: description || originalProject.description,
          category: category || 'general',
          subcategory: subcategory,
          difficulty_level: originalProject.difficulty_level,
          estimated_hours: originalProject.estimated_hours,
          total_points: originalProject.total_points,
          learning_objectives: originalProject.learning_objectives,
          prerequisites: originalProject.prerequisites,
          skills_covered: skills_covered,
          technologies_used: technologies_used,
          tags: originalProject.tags,
          created_by: userId
        },
        { transaction }
      );

      // Duplicate S3 files if they exist (outside transaction)
      let newDatasetS3Urls = [];
      if (
        originalProject.dataset_s3_url &&
        Array.isArray(originalProject.dataset_s3_url) &&
        originalProject.dataset_s3_url.length > 0
      ) {
        try {
          newDatasetS3Urls = await Promise.all(
            originalProject.dataset_s3_url.map(async originalKey => {
              try {
                // Generate new key with template project ID
                const newKey = originalKey.replace(
                  /projects\/[^/]+\//,
                  `projects/${templateProject.id}/`
                );

                // Copy the S3 object
                await s3Service.copyFile(originalKey, newKey);
                return newKey;
              } catch (err) {
                logger.warn(`Failed to copy S3 file ${originalKey}:`, err);
                return null;
              }
            })
          );

          // Filter out failed copies
          newDatasetS3Urls = newDatasetS3Urls.filter(key => key !== null);
        } catch (error) {
          logger.warn('Error duplicating S3 files for template:', error);
        }
      }

      // Commit transaction before S3 operations
      await transaction.commit();

      // Update project with S3 URLs after transaction commit
      if (newDatasetS3Urls.length > 0) {
        await templateProject.update({ dataset_s3_url: newDatasetS3Urls });
      }

      logger.info(
        `Template created from project: ${originalProject.title} by user ${userId}`
      );

      return {
        id: template.id,
        projectId: templateProject.id,
        title: template.template_name,
        description: template.template_description,
        category: template.category,
        subcategory: template.subcategory,
        difficulty_level: template.difficulty_level,
        estimated_hours: template.estimated_hours,
        total_points: template.total_points,
        learning_objectives: template.learning_objectives,
        prerequisites: template.prerequisites,
        skills_covered: template.skills_covered,
        technologies_used: template.technologies_used,
        tags: template.tags,
        isPublic: template.is_public,
        createdAt: template.created_at
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update project template
   */
  async updateProjectTemplate(req) {
    const transaction = await sequelize.transaction();

    try {
      const {
        title,
        description,
        courseId,
        projectType,
        difficulty_level,
        estimatedHours,
        totalPoints,
        dueDate,
        startDate,
        instructions,
        project_overview,
        learning_objectives,
        prerequisites,
        tags,
        isTemplate,
        templateCategory,
        templateSubcategory,
        isScreen,
        status,
        instructorIds,
        teachingAssId,
        maxSubmissions,
        lateSubmissionsAllowed,
        sandbox_time_duration,
        late_submission_days_allowed,
        projectId,
        skillsCovered,
        technologies_used
      } = req.body;

      const { id: templateId } = req.params;

      const template = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false },
        transaction
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      // Check permissions
      if (template.created_by !== req.user.id) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only creator can update template'
        );
      }

      const updateTemplateProjectData = {};
      if (title) {
        updateTemplateProjectData.title = title;
        updateTemplateProjectData.template_name = title;
      }
      if (courseId) updateTemplateProjectData.course_id = courseId;
      if (description) {
        updateTemplateProjectData.description = description;
        updateTemplateProjectData.template_description = description;
      }
      if (instructorIds)
        updateTemplateProjectData.instructor_id = instructorIds;
      if (teachingAssId)
        updateTemplateProjectData.teaching_ass_id = teachingAssId;
      if (projectType) updateTemplateProjectData.type = projectType;
      if (difficulty_level)
        updateTemplateProjectData.difficulty_level = difficulty_level;
      if (totalPoints) updateTemplateProjectData.total_points = totalPoints;
      if (estimatedHours)
        updateTemplateProjectData.estimated_hours = estimatedHours;
      if (tags) updateTemplateProjectData.tags = tags;
      if (project_overview)
        updateTemplateProjectData.project_overview = project_overview;
      if (learning_objectives)
        updateTemplateProjectData.learning_objectives = learning_objectives;
      if (prerequisites)
        updateTemplateProjectData.prerequisites = prerequisites;
      if (instructions) updateTemplateProjectData.instructions = instructions;
      if (maxSubmissions)
        updateTemplateProjectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed)
        updateTemplateProjectData.late_submission_allowed =
          lateSubmissionsAllowed;
      if (dueDate) updateTemplateProjectData.due_date = dueDate;
      if (status) updateTemplateProjectData.status = status;
      if (isScreen) updateTemplateProjectData.isScreen = isScreen;
      if (startDate) updateTemplateProjectData.start_date = startDate;
      if (isTemplate) updateTemplateProjectData.is_template = isTemplate;
      if (templateCategory)
        updateTemplateProjectData.category = templateCategory;
      if (templateSubcategory)
        updateTemplateProjectData.subcategory = templateSubcategory;
      if (late_submission_days_allowed)
        updateTemplateProjectData.late_submission_days_allowed =
          late_submission_days_allowed;
      if (sandbox_time_duration)
        updateTemplateProjectData.sandbox_time_duration = sandbox_time_duration;
      if (skillsCovered)
        updateTemplateProjectData.skills_covered = skillsCovered;
      if (technologies_used)
        updateTemplateProjectData.technologies_used = technologies_used;

      if (projectId) {
        updateTemplateProjectData.project_id = projectId;
        updateTemplateProjectData.id = projectId;
      }

      // Update project if project data exists
      if (Object.keys(updateTemplateProjectData).length > 0) {
        const projectToUpdate = await Project.findOne({
          where: { id: template.project_id, is_deleted: false },
          transaction
        });

        if (projectToUpdate) {
          await projectToUpdate.update(updateTemplateProjectData, {
            transaction
          });
        }
      }

      // Update template record
      const templateUpdateData = {};
      if (title) templateUpdateData.template_name = title;
      if (description) templateUpdateData.template_description = description;
      if (templateCategory) templateUpdateData.category = templateCategory;
      if (templateSubcategory)
        templateUpdateData.subcategory = templateSubcategory;
      if (difficulty_level)
        templateUpdateData.difficulty_level = difficulty_level;
      if (estimatedHours) templateUpdateData.estimated_hours = estimatedHours;
      if (totalPoints) templateUpdateData.total_points = totalPoints;
      if (learning_objectives)
        templateUpdateData.learning_objectives = learning_objectives;
      if (prerequisites) templateUpdateData.prerequisites = prerequisites;
      if (skillsCovered) templateUpdateData.skills_covered = skillsCovered;
      if (technologies_used)
        templateUpdateData.technologies_used = technologies_used;
      if (tags) templateUpdateData.tags = tags;

      if (Object.keys(templateUpdateData).length > 0) {
        await template.update(templateUpdateData, { transaction });
      }

      await transaction.commit();

      // Fetch updated template with complete details
      const updatedTemplate = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false },
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status']
          },
          {
            model: User,
            as: 'createdBy',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      logger.info(
        `Project template updated: ${updatedTemplate.template_name} by user ${req.user.id}`
      );

      return {
        id: updatedTemplate.id,
        title: updatedTemplate.template_name,
        description: updatedTemplate.template_description,
        template_category: updatedTemplate.category,
        template_subcategory: updatedTemplate.subcategory,
        difficulty_level: updatedTemplate.difficulty_level,
        estimated_hours: updatedTemplate.estimated_hours,
        total_points: updatedTemplate.total_points,
        learning_objectives: updatedTemplate.learning_objectives,
        prerequisites: updatedTemplate.prerequisites,
        skills_covered: updatedTemplate.skills_covered,
        technologies_used: updatedTemplate.technologies_used,
        tags: updatedTemplate.tags,
        isPublic: updatedTemplate.is_public,
        updatedAt: updatedTemplate.updatedAt,
        projectId: updatedTemplate.project_id
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get project templates with filtering
   */
  async getProjectTemplates(options = {}) {
    try {
      const {
        category,
        subcategory,
        difficultyLevel,
        isFeatured,
        isPublic,
        page = 1,
        limit = 10,
        search
      } = options;

      const whereClause = {
        is_deleted: false
      };

      if (category) {
        whereClause.category = category;
      }

      if (subcategory) {
        whereClause.subcategory = subcategory;
      }

      if (difficultyLevel) {
        whereClause.difficulty_level = difficultyLevel;
      }

      if (isFeatured !== undefined) {
        whereClause.is_featured = isFeatured;
      }

      if (isPublic !== undefined) {
        whereClause.is_public = isPublic;
      }

      const offset = (page - 1) * limit;

      const { count, rows: templates } = await ProjectTemplate.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status'],
            where: search
              ? {
                  [Op.or]: [
                    { title: { [Op.iLike]: `%${search}%` } },
                    { description: { [Op.iLike]: `%${search}%` } }
                  ]
                }
              : undefined
          },
          {
            model: User,
            as: 'createdBy',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [
          ['is_featured', 'DESC'],
          ['rating', 'DESC'],
          ['usage_count', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit,
        offset
      });

      return {
        templates,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(templateId) {
    try {
      const template = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false },
        attributes: [
          'id',
          'project_id',
          'category',
          'subcategory',
          'skills_covered',
          'technologies_used'
        ],
        include: [
          {
            model: User,
            as: 'createdBy',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      const projectWithDetails =
        await enhancedProjectUtils.getProjectWithCompleteDetails(
          template?.project_id
        );

      return {
        template: template.toJSON(),
        project: projectWithDetails
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Duplicate project from template
   */
  async duplicateProjectFromTemplate(templateId, newProjectData, userId) {
    const transaction = await sequelize.transaction();

    try {
      // Get template with project details
      const template = await ProjectTemplate.findOne({
        where: {
          id: templateId,
          is_deleted: false
        },
        transaction
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      // Get complete project details with checkpoints and rubrics
      const originalProject = await Project.findOne({
        where: { id: template.project_id, is_deleted: false },
        include: [
          {
            model: Checkpoint,
            as: 'checkpoints',
            required: false,
            include: [
              {
                model: Rubric,
                as: 'rubrics',
                required: false
              }
            ]
          }
        ],
        transaction
      });

      if (!originalProject) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template project not found');
      }

      // Create new project with template data using conditional checks
      const projectData = {
        created_by: userId,
        status: 'draft'
      };

      if (newProjectData.title) {
        projectData.title = newProjectData.title;
      } else if (originalProject.title) {
        projectData.title = `${originalProject.title} (Copy)`;
      }

      if (newProjectData.description) {
        projectData.description = newProjectData.description;
      } else if (originalProject.description) {
        projectData.description = originalProject.description;
      }

      // if (newProjectData.courseId) projectData.course_id = newProjectData.courseId;
      if (originalProject.instructions)
        projectData.instructions = originalProject.instructions;
      if (originalProject.difficulty_level)
        projectData.difficulty_level = originalProject.difficulty_level;
      if (originalProject.estimated_hours)
        projectData.estimated_hours = originalProject.estimated_hours;
      if (originalProject.total_points)
        projectData.total_points = originalProject.total_points;
      if (originalProject.learning_objectives)
        projectData.learning_objectives = originalProject.learning_objectives;
      if (originalProject.prerequisites)
        projectData.prerequisites = originalProject.prerequisites;
      if (originalProject.project_overview)
        projectData.project_overview = originalProject.project_overview;
      if (originalProject.tags) projectData.tags = originalProject.tags;
      if (originalProject.type) projectData.type = originalProject.type;
      if (originalProject.category_id)
        projectData.category_id = originalProject.category_id;

      const newProject = await Project.create(projectData, { transaction });

      // Duplicate checkpoints with their rubrics
      if (
        originalProject.checkpoints &&
        originalProject.checkpoints.length > 0
      ) {
        for (const checkpoint of originalProject.checkpoints) {
          const checkpointData = {
            project_id: newProject.id,
            created_by: userId,
            status: 'draft'
          };

          if (checkpoint.title) checkpointData.title = checkpoint.title;
          if (checkpoint.description)
            checkpointData.description = checkpoint.description;
          if (checkpoint.checkpoint_number)
            checkpointData.checkpoint_number = checkpoint.checkpoint_number;
          if (checkpoint.due_date)
            checkpointData.due_date = checkpoint.due_date;
          if (checkpoint.start_date)
            checkpointData.start_date = checkpoint.start_date;
          if (checkpoint.weight_percentage)
            checkpointData.weight_percentage = checkpoint.weight_percentage;
          if (checkpoint.is_required !== undefined)
            checkpointData.is_required = checkpoint.is_required;
          if (checkpoint.metadata)
            checkpointData.metadata = checkpoint.metadata;

          const newCheckpoint = await Checkpoint.create(checkpointData, {
            transaction
          });

          // Duplicate rubrics for this checkpoint
          if (checkpoint.rubrics && checkpoint.rubrics.length > 0) {
            const rubricPromises = checkpoint.rubrics.map(rubric => {
              const rubricData = {
                project_id: newProject.id,
                checkpoint_id: newCheckpoint.id,
                created_by: userId
              };

              if (rubric.title) rubricData.title = rubric.title;
              if (rubric.description)
                rubricData.description = rubric.description;
              if (rubric.criteria) rubricData.criteria = rubric.criteria;
              if (rubric.total_points)
                rubricData.total_points = rubric.total_points;
              if (rubric.grading_scale)
                rubricData.grading_scale = rubric.grading_scale;
              if (rubric.is_template !== undefined)
                rubricData.is_template = rubric.is_template;
              if (rubric.template_name)
                rubricData.template_name = rubric.template_name;

              return Rubric.create(rubricData, { transaction });
            });
            await Promise.all(rubricPromises);
          }
        }
      }

      // Update original template usage count
      const templateUpdateData = {};
      if (template.usage_count !== undefined) {
        templateUpdateData.usage_count = template.usage_count + 1;
      } else {
        templateUpdateData.usage_count = 1;
      }
      templateUpdateData.last_used = new Date();

      await template.update(templateUpdateData, { transaction });

      // Commit transaction before S3 operations
      await transaction.commit();

      // Duplicate S3 files if they exist (outside transaction)
      let newDatasetS3Urls = [];
      if (
        originalProject.dataset_s3_url &&
        Array.isArray(originalProject.dataset_s3_url) &&
        originalProject.dataset_s3_url.length > 0
      ) {
        try {
          newDatasetS3Urls = await Promise.all(
            originalProject.dataset_s3_url.map(async originalKey => {
              try {
                const newKey = originalKey.replace(
                  /projects\/[^/]+\//,
                  `projects/${newProject.id}/`
                );
                await s3Service.copyFile(originalKey, newKey);
                return newKey;
              } catch (err) {
                logger.warn(`Failed to copy S3 file ${originalKey}:`, err);
                return null;
              }
            })
          );

          newDatasetS3Urls = newDatasetS3Urls.filter(key => key !== null);
          if (newDatasetS3Urls.length > 0) {
            await newProject.update({ dataset_s3_url: newDatasetS3Urls });
          }
        } catch (error) {
          logger.warn('Error duplicating S3 files:', error);
        }
      }

      logger.info(
        `Project duplicated from template: ${template.template_name} by user ${userId}`
      );

      // Fetch complete project details with all relationships
      // Use utility function to get complete project details
      return await enhancedProjectUtils.getProjectWithCompleteDetails(
        newProject.id
      );
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Rate project template
   */
  async rateProjectTemplate(templateId, rating, userId) {
    try {
      const template = await ProjectTemplate.findOne({
        where: {
          id: templateId,
          is_deleted: false
        }
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      // Calculate new average rating
      const newRatingCount = template.rating_count + 1;
      const newRating =
        (template.rating * template.rating_count + rating) / newRatingCount;

      await template.update({
        rating: newRating,
        rating_count: newRatingCount
      });

      logger.info(
        `Template rated: ${template.template_name} with rating ${rating} by user ${userId}`
      );
      return template;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete project template
   */
  async deleteProjectTemplate(templateId, userId) {
    const transaction = await sequelize.transaction();

    try {
      const template = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false },
        transaction
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      // Check permissions
      if (template.created_by !== userId) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only creator can delete template'
        );
      }

      // Get project with S3 URLs before deletion
      const project = await Project.findOne({
        where: { id: template.project_id, is_deleted: false },
        transaction
      });

      let s3UrlsToDelete = [];
      if (
        project &&
        project.dataset_s3_url &&
        Array.isArray(project.dataset_s3_url)
      ) {
        s3UrlsToDelete = [...project.dataset_s3_url];
      }

      // Delete rubrics
      if (project) {
        await Rubric.update(
          {
            is_deleted: true,
            deleted_by: userId,
            deleted_at: new Date()
          },
          {
            where: { project_id: project.id },
            transaction
          }
        );

        // Delete checkpoints
        await Checkpoint.update(
          {
            is_deleted: true,
            deleted_by: userId,
            deleted_at: new Date()
          },
          {
            where: { project_id: project.id },
            transaction
          }
        );

        // Delete project
        await project.update(
          {
            is_deleted: true,
            deleted_by: userId,
            deleted_at: new Date()
          },
          { transaction }
        );
      }

      // Delete template
      await template.update(
        {
          is_deleted: true,
          deleted_by: userId,
          deleted_at: new Date()
        },
        { transaction }
      );

      await transaction.commit();

      // Delete S3 files after successful database transaction
      if (s3UrlsToDelete.length > 0) {
        try {
          await Promise.all(
            s3UrlsToDelete.map(async s3Key => {
              try {
                await s3Service.deleteFile(s3Key);
              } catch (err) {
                logger.warn(`Failed to delete S3 file ${s3Key}:`, err);
              }
            })
          );
        } catch (error) {
          logger.warn('Error deleting S3 files:', error);
        }
      }

      logger.info(
        `Template deleted: ${template.template_name} by user ${userId}`
      );
      return { message: 'Template deleted successfully' };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Feature or unfeature a project template
   */
  async featureProjectTemplate(templateId, featured, userId) {
    try {
      const template = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false }
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      await template.update({
        is_featured: featured
      });

      return template;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Approve or reject a project template
   */
  async approveProjectTemplate(
    templateId,
    approved,
    reviewerId,
    reviewNotes = null
  ) {
    try {
      const template = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false }
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      const reviewStatus = approved ? 'approved' : 'rejected';

      await template.update({
        review_status: reviewStatus,
        reviewed_by: reviewerId,
        review_notes: reviewNotes,
        review_date: new Date()
      });

      return template;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Archive a project template
   */
  async archiveProjectTemplate(templateId, userId, archiveReason = null) {
    try {
      const template = await ProjectTemplate.findOne({
        where: { id: templateId, is_deleted: false }
      });

      if (!template) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Template not found');
      }

      await template.update({
        review_status: 'archived',
        review_notes: archiveReason || 'Template archived by user'
      });

      return template;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Assign users to project
   */
  async assignUsersToProject(projectId, assignments, assignedBy) {
    try {
      const assignmentPromises = assignments.map(assignment =>
        ProjectAssignment.create({
          project_id: projectId,
          user_id: assignment.userId,
          role: assignment.role,
          assignment_type: assignment.assignmentType || 'primary',
          permissions: assignment.permissions || {},
          assigned_by: assignedBy,
          start_date: assignment.startDate,
          end_date: assignment.endDate,
          notes: assignment.notes
        })
      );

      const createdAssignments = await Promise.all(assignmentPromises);

      logger.info(
        `Users assigned to project ${projectId}: ${assignments.length} assignments`
      );
      return createdAssignments;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Remove user assignment from project
   */
  async removeUserAssignment(projectId, userId) {
    try {
      const assignment = await ProjectAssignment.findOne({
        where: { project_id: projectId, user_id: userId }
      });

      if (!assignment) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Assignment not found');
      }

      await assignment.update({ is_active: false });

      logger.info(
        `User assignment removed: project ${projectId}, user ${userId}`
      );
      return { success: true };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get project assignments
   */
  async getProjectAssignments(projectId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: { project_id: projectId, is_active: true },
        include: [
          {
            model: User,
            as: 'assignedUser',
            attributes: ['id', 'name', 'email', 'role']
          }
        ],
        order: [
          ['role', 'ASC'],
          ['assigned_at', 'ASC']
        ]
      });

      return assignments;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Save project as draft
   */
  async saveProjectAsDraft(projectId, userId) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, is_deleted: false }
      });

      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      // Check permissions
      if (project.created_by !== userId) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only creator can save project'
        );
      }

      await project.update({
        status: 'draft',
        published_at: null,
        published_by: null
      });

      logger.info(`Project saved as draft: ${project.title} by user ${userId}`);
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get project assignment statistics
   */
  async getProjectAssignmentStats(projectId) {
    try {
      const stats = await ProjectAssignment.findAll({
        where: { project_id: projectId, is_active: true },
        attributes: [
          'role',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['role']
      });

      const totalAssignments = await ProjectAssignment.count({
        where: { project_id: projectId, is_active: true }
      });

      return {
        totalAssignments,
        byRole: stats.reduce((acc, stat) => {
          acc[stat.role] = parseInt(stat.dataValues.count);
          return acc;
        }, {}),
        roles: stats.map(stat => stat.role)
      };
    } catch (error) {
      logger.error('Error getting project assignment stats:', error);
      return {
        totalAssignments: 0,
        byRole: {},
        roles: []
      };
    }
  }

  /**
   * Get projects by user assignment
   */
  async getProjectsByUserAssignment(userId, role = null) {
    try {
      const whereClause = {
        user_id: userId,
        is_active: true
      };

      if (role) {
        whereClause.role = role;
      }

      const assignments = await ProjectAssignment.findAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              },
              {
                model: User,
                as: 'creator',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ],
        order: [['assigned_at', 'DESC']]
      });

      return assignments.map(assignment => ({
        assignmentId: assignment.id,
        role: assignment.role,
        assignmentType: assignment.assignment_type,
        assignedAt: assignment.assigned_at,
        startDate: assignment.start_date,
        endDate: assignment.end_date,
        project: assignment.project
      }));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user's project workload
   */
  async getUserProjectWorkload(userId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: {
          user_id: userId,
          is_active: true
        },
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'status', 'due_date', 'estimated_hours']
          }
        ]
      });

      const workload = {
        totalProjects: assignments.length,
        activeProjects: assignments.filter(
          a => a.project.status === 'published'
        ).length,
        draftProjects: assignments.filter(a => a.project.status === 'draft')
          .length,
        totalEstimatedHours: assignments.reduce(
          (sum, a) => sum + (a.project.estimated_hours || 0),
          0
        ),
        byRole: {},
        upcomingDeadlines: []
      };

      // Group by role
      assignments.forEach(assignment => {
        const role = assignment.role;
        if (!workload.byRole[role]) {
          workload.byRole[role] = {
            count: 0,
            projects: []
          };
        }
        workload.byRole[role].count++;
        workload.byRole[role].projects.push(assignment.project);
      });

      // Get upcoming deadlines (next 30 days)
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

      workload.upcomingDeadlines = assignments
        .filter(
          a => a.project.due_date && a.project.due_date <= thirtyDaysFromNow
        )
        .map(a => ({
          projectId: a.project.id,
          projectTitle: a.project.title,
          dueDate: a.project.due_date,
          daysUntilDue: Math.ceil(
            (a.project.due_date - new Date()) / (1000 * 60 * 60 * 24)
          )
        }))
        .sort((a, b) => a.daysUntilDue - b.daysUntilDue);

      return workload;
    } catch (error) {
      throw error;
    }
  }
}

export default new EnhancedProjectService();
