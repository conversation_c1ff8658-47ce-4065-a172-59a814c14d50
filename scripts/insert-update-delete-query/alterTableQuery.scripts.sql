--Porject table alteration script--
ALTER TABLE projects
ADD COLUMN sandbox_time_duration VARCHAR(5) DEFAULT null,
ADD COLUMN late_submission_days_allowed INTEGER DEFAULT 0;

COMMENT ON COLUMN projects.sandbox_time_duration IS 'Allowed sandbox duration in HH:MM format';
COMMENT ON COLUMN projects.late_submission_days_allowed IS 'Number of days late submission is allowed';


--- Alter dataset_s3_url column from TEXT to JSONB
-- Step 1: Drop default and set all existing NULL values to '[]'
-- This ensures that the column can be safely converted to jsonb type
ALTER TABLE projects ALTER COLUMN dataset_s3_url DROP DEFAULT;

-- Force reset values to empty JSON array
UPDATE projects SET dataset_s3_url = '[]';

-- Now change type
ALTER TABLE projects ALTER COLUMN dataset_s3_url TYPE jsonb USING '[]'::jsonb, ALTER COLUMN dataset_s3_url SET DEFAULT '[]'::jsonb;
COMMENT ON COLUMN projects.dataset_s3_url IS 'Array of S3 URLs for project dataset files';

-- Make course_id column NOT NULL
ALTER TABLE projects ALTER COLUMN course_id DROP NOT NULL;

-- Make title column in rubrics table nullable
ALTER TABLE rubrics ALTER COLUMN title DROP NOT NULL;

--- Add start_date column to checkpoints table
ALTER TABLE checkpoints
ADD COLUMN start_date TIMESTAMP NULL;

COMMENT ON COLUMN checkpoints.start_date IS 'When this checkpoint should be started';

-- Modify sandbox_time_duration to allow HHH:MM format and drop NOT NULL constraint
ALTER TABLE projects
ALTER COLUMN sandbox_time_duration TYPE VARCHAR(6),
ALTER COLUMN sandbox_time_duration DROP NOT NULL;

COMMENT ON COLUMN projects.sandbox_time_duration IS 'Allowed sandbox duration in HHH:MM format';


-- 1. Add the new column
ALTER TABLE rubrics
ADD COLUMN checkpoint_id UUID;

-- 2. Add the foreign key constraint
ALTER TABLE rubrics
ADD CONSTRAINT fk_rubrics_checkpoint
FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id);

-- 3. Backfill data based on project_id
UPDATE rubrics 
SET checkpoint_id = (
  SELECT c.id
  FROM checkpoints c
  WHERE c.project_id = rubrics.project_id
  ORDER BY c.created_at ASC
  LIMIT 1
)
WHERE checkpoint_id IS NULL;

-- 4. Make column NOT NULL
ALTER TABLE rubrics
ALTER COLUMN checkpoint_id SET NOT NULL;


-- adding publication and deletion tracking columns to projects table
ALTER TABLE projects
ADD COLUMN published_by UUID REFERENCES users(id),
ADD COLUMN published_at TIMESTAMP,
ADD COLUMN is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN deleted_by UUID REFERENCES users(id);

COMMENT ON COLUMN projects.published_by IS 'User who published the project';
COMMENT ON COLUMN projects.published_at IS 'Timestamp when the project was published';

-- delete the unique constraint on project_id and checkpoint_number
DROP INDEX checkpoints_project_id_checkpoint_number;

--course_id delete and new course_id create with REFERENCES in LtiContext tabel id

ALTER TABLE public.projects RENAME COLUMN "course_id" TO "course_id_copy"; 

ALTER TABLE public.projects DROP COLUMN "course_id_copy";
ALTER TABLE public.projects ADD COLUMN course_id UUID;

-- 1) Drop old FK (if it exists)
ALTER TABLE public.projects DROP CONSTRAINT IF EXISTS projects_course_id_fkey;

-- 2) (Optional) clean mismatches
UPDATE public.projects p SET course_id = NULL WHERE course_id IS NOT NULL AND NOT EXISTS ( SELECT 1 FROM public.lti_contexts c WHERE c.id = p.course_id );

UPDATE public.projects SET course_id = 'PUT-NEW-LTICONTEXT-UUID-HERE'::uuid WHERE course_id_copy IS NOT NULL;

-- 3) Add the FK (no dot-inside-quotes!)
ALTER TABLE public.projects ADD CONSTRAINT projects_course_id_fkey FOREIGN KEY (course_id) REFERENCES public.lti_contexts(id) ON UPDATE CASCADE ON DELETE SET NULL;

-- project_templates table alteration script
ALTER TABLE public.project_templates
  ADD COLUMN IF NOT EXISTS total_points INTEGER;

COMMENT ON COLUMN public.project_templates.total_points
  IS 'total points for the project';

ALTER TABLE public.project_templates
  ADD COLUMN IF NOT EXISTS learning_objectives TEXT;

COMMENT ON COLUMN public.project_templates.learning_objectives
  IS 'learning objectives for the project';

ALTER TABLE public.project_templates
  ADD COLUMN IF NOT EXISTS prerequisites TEXT;

COMMENT ON COLUMN public.project_templates.prerequisites
  IS 'Prerequisite topics or projects';

ALTER TABLE public.project_templates
  ADD COLUMN IF NOT EXISTS deleted_by UUID;

-- 2) FK to users(id)
ALTER TABLE public.project_templates
  DROP CONSTRAINT IF EXISTS project_templates_deleted_by_fkey;

ALTER TABLE public.project_templates
  ADD CONSTRAINT project_templates_deleted_by_fkey
  FOREIGN KEY (deleted_by) REFERENCES public.users(id)
  ON UPDATE CASCADE ON DELETE SET NULL;

  ALTER TABLE public.project_templates
  ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN;

-- Ensure default + backfill + not-null
ALTER TABLE public.project_templates
  ALTER COLUMN is_deleted SET DEFAULT false;

UPDATE public.project_templates
SET is_deleted = false
WHERE is_deleted IS NULL;

ALTER TABLE public.project_templates
  ALTER COLUMN is_deleted SET NOT NULL;

COMMENT ON COLUMN public.project_templates.is_deleted
  IS 'Flag to indicate if the project is deleted';

-- Add new columns to projects table
  ALTER TABLE projects
ADD COLUMN is_team_project BOOLEAN DEFAULT false;

ALTER TABLE projects
ADD COLUMN max_team_size INTEGER DEFAULT 1;

ALTER TABLE projects
ADD COLUMN team_formation_deadline TIMESTAMP;

ALTER TABLE projects
ADD COLUMN is_template BOOLEAN DEFAULT false;

ALTER TABLE projects
ADD COLUMN template_name VARCHAR(255);

-- Add new colums to rubrics table
ALTER TABLE rubrics
ADD COLUMN is_deleted BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE rubrics
ADD COLUMN deleted_by UUID REFERENCES users(id);

ALTER TABLE rubrics
ADD COLUMN deleted_at TIMESTAMP;  

-- Add new colums to checkpoints table
ALTER TABLE checkpoints
ADD COLUMN is_template BOOLEAN DEFAULT false;

ALTER TABLE checkpoints
ADD COLUMN template_name VARCHAR(255);

ALTER TABLE checkpoints
ADD COLUMN is_deleted BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE checkpoints
ADD COLUMN deleted_by UUID REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE checkpoints
ADD COLUMN deleted_at TIMESTAMP;
